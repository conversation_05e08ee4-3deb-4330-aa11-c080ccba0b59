import 'dart:developer' as developer;
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

/// Centralized logging service that integrates Firebase Crashlytics
/// with local logging for comprehensive error tracking and debugging.
class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  static LoggingService get instance => _instance;

  LoggingService._internal();

  FirebaseCrashlytics? _crashlytics;
  bool _isInitialized = false;
  bool _isCrashlyticsAvailable = false;

  /// Initialize the logging service with Firebase Crashlytics
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Crashlytics is not available on web platforms
    if (!kIsWeb) {
      _crashlytics = FirebaseCrashlytics.instance;
      _isCrashlyticsAvailable = true;

      // Configure Crashlytics collection based on build mode
      if (kDebugMode) {
        // Disable in debug mode to avoid cluttering production data
        await _crashlytics!.setCrashlyticsCollectionEnabled(false);
      } else {
        // Enable in release mode
        await _crashlytics!.setCrashlyticsCollectionEnabled(true);
      }
    }

    _isInitialized = true;
  }

  /// Set user identifier for crash reports
  Future<void> setUserIdentifier(String userId) async {
    if (!_isInitialized || !_isCrashlyticsAvailable) return;

    try {
      await _crashlytics!.setUserIdentifier(userId);
      logInfo('LoggingService', 'User identifier set: $userId');
    } catch (e) {
      developer.log(
        'Failed to set user identifier: $e',
        name: 'LoggingService',
        level: 900,
      );
    }
  }

  /// Set custom key-value pairs for crash reports
  Future<void> setCustomKey(String key, dynamic value) async {
    if (!_isInitialized || !_isCrashlyticsAvailable) return;

    try {
      await _crashlytics!.setCustomKey(key, value);
    } catch (e) {
      developer.log(
        'Failed to set custom key $key: $e',
        name: 'LoggingService',
        level: 900,
      );
    }
  }

  /// Log informational messages
  void logInfo(String tag, String message, {Map<String, dynamic>? params}) {
    final logMessage = '$tag: $message';

    // Add to Crashlytics breadcrumbs for context
    if (_isInitialized && _isCrashlyticsAvailable) {
      _crashlytics!.log(logMessage);
    }

    // Local debug logging
    developer.log(
      message,
      name: tag,
      level: 800,
      sequenceNumber: DateTime.now().millisecondsSinceEpoch,
    );

    // Debug print for development
    if (kDebugMode) {
      debugPrint('INFO [$tag]: $message ${params ?? ''}');
    }
  }

  /// Log warning messages
  void logWarning(String tag, String message, {Map<String, dynamic>? params}) {
    final logMessage = 'WARNING [$tag]: $message';

    // Add to Crashlytics breadcrumbs
    if (_isInitialized && _isCrashlyticsAvailable) {
      _crashlytics!.log(logMessage);
    }

    // Local debug logging
    developer.log(
      message,
      name: tag,
      level: 900,
      sequenceNumber: DateTime.now().millisecondsSinceEpoch,
    );

    // Debug print for development
    if (kDebugMode) {
      debugPrint('WARNING [$tag]: $message ${params ?? ''}');
    }
  }

  /// Log error messages and optionally report to Crashlytics
  Future<void> logError(
    dynamic error,
    StackTrace? stackTrace,
    String tag,
    String message, {
    bool fatal = false,
    Map<String, dynamic>? params,
  }) async {
    final logMessage = 'ERROR [$tag]: $message';

    // Report to Crashlytics if initialized and available
    if (_isInitialized && _isCrashlyticsAvailable) {
      try {
        await _crashlytics!.recordError(
          error,
          stackTrace,
          reason: message,
          fatal: fatal,
        );
      } catch (e) {
        developer.log(
          'Failed to record error to Crashlytics: $e',
          name: 'LoggingService',
          level: 1000,
        );
      }
    }

    // Local error logging
    developer.log(
      message,
      name: tag,
      error: error,
      stackTrace: stackTrace,
      level: 1000,
      sequenceNumber: DateTime.now().millisecondsSinceEpoch,
    );

    // Debug print for development
    if (kDebugMode) {
      debugPrint('$logMessage\nError: $error\nStack: $stackTrace');
    }
  }

  /// Log non-fatal exceptions
  Future<void> logException(
    Exception exception,
    StackTrace? stackTrace,
    String tag,
    String message, {
    Map<String, dynamic>? params,
  }) async {
    await logError(
      exception,
      stackTrace,
      tag,
      message,
      fatal: false,
      params: params,
    );
  }

  /// Force a crash for testing purposes (debug mode only)
  void testCrash() {
    if (kDebugMode && _isInitialized && _isCrashlyticsAvailable) {
      logWarning('LoggingService', 'Testing crash reporting...');
      _crashlytics!.crash();
    } else if (!_isCrashlyticsAvailable) {
      logWarning('LoggingService', 'Crashlytics not available on web platform');
    }
  }

  /// Record a test non-fatal error for testing purposes
  Future<void> testNonFatalError() async {
    if (kDebugMode && _isInitialized && _isCrashlyticsAvailable) {
      logWarning('LoggingService', 'Testing non-fatal error reporting...');
      await _crashlytics!.recordError(
        Exception('This is a test non-fatal error'),
        StackTrace.current,
        reason: 'Testing Crashlytics integration',
        fatal: false,
      );
    } else if (!_isCrashlyticsAvailable) {
      logWarning('LoggingService', 'Crashlytics not available on web platform');
    }
  }

  /// Check if the logging service is initialized
  bool get isInitialized => _isInitialized;
}

import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'firestore.dart';
import 'logging_service.dart';
import '../widgets/in_app_notification.dart';

/// Service for managing Firebase Cloud Messaging (FCM) integration
///
/// This service handles:
/// - FCM token management and storage
/// - Permission requests for notifications
/// - Message handling for foreground, background, and terminated states
/// - Topic subscription and unsubscription
/// - Integration with existing FirestoreService for data persistence
class NotificationService {
  static NotificationService? _instance;
  static const String _logTag = 'NotificationService';

  bool _isInitialized = false;
  String? _currentToken;
  String? _deviceId;
  GlobalKey<NavigatorState>? _navigatorKey;
  final StreamController<RemoteMessage> _messageController =
      StreamController<RemoteMessage>.broadcast();
  final StreamController<String?> _tokenController =
      StreamController<String?>.broadcast();

  /// Private constructor for singleton pattern
  NotificationService._();

  /// Get the singleton instance
  static NotificationService get instance {
    _instance ??= NotificationService._();
    return _instance!;
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Get the current FCM token
  String? get currentToken => _currentToken;

  /// Get the current device ID
  String? get deviceId => _deviceId;

  /// Stream of incoming messages
  Stream<RemoteMessage> get messageStream => _messageController.stream;

  /// Stream of token updates
  Stream<String?> get tokenStream => _tokenController.stream;

  /// Set the navigator key for in-app notifications
  void setNavigatorKey(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
  }

  /// Initialize the notification service
  /// Must be called before using the service
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggingService.instance.logInfo(
        _logTag,
        'NotificationService already initialized',
      );
      return;
    }

    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Initializing NotificationService',
      );

      // Generate device ID
      await _generateDeviceId();

      // Request permission for notifications
      await _requestPermission();

      // Get initial FCM token
      await _getToken();

      // Set up message handlers
      _setupMessageHandlers();

      // Listen for token refresh
      FirebaseMessaging.instance.onTokenRefresh.listen(_onTokenRefresh);

      _isInitialized = true;

      LoggingService.instance.logInfo(
        _logTag,
        'NotificationService initialized successfully',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to initialize NotificationService',
      );
      rethrow;
    }
  }

  /// Request notification permissions
  Future<NotificationSettings> _requestPermission() async {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Requesting notification permissions',
      );

      final settings = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      LoggingService.instance.logInfo(
        _logTag,
        'Permission status: ${settings.authorizationStatus}',
      );

      return settings;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to request notification permissions',
      );
      rethrow;
    }
  }

  /// Generate unique device ID for multi-device token management
  Future<void> _generateDeviceId() async {
    try {
      LoggingService.instance.logInfo(_logTag, 'Generating device ID');

      final deviceInfo = DeviceInfoPlugin();
      String deviceId;

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        // Use a combination of model and androidId for uniqueness
        deviceId = 'android_${androidInfo.model}_${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        // Use identifierForVendor for iOS
        deviceId = 'ios_${iosInfo.identifierForVendor ?? 'unknown'}';
      } else {
        // Fallback for other platforms
        deviceId = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
      }

      // Clean up device ID to ensure it's safe for use as a map key
      _deviceId = deviceId.replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_');

      LoggingService.instance.logInfo(
        _logTag,
        'Generated device ID: $_deviceId',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to generate device ID',
      );
      // Fallback to timestamp-based ID
      _deviceId = 'fallback_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Get FCM token and store it
  Future<void> _getToken() async {
    try {
      LoggingService.instance.logInfo(_logTag, 'Getting FCM token');

      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        _currentToken = token;
        _tokenController.add(token);

        LoggingService.instance.logInfo(
          _logTag,
          'FCM token obtained: ${token.substring(0, 20)}...',
        );

        // Store token in Firestore if user is logged in
        await _storeTokenInFirestore(token);
      } else {
        LoggingService.instance.logInfo(_logTag, 'FCM token is null');
      }
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to get FCM token',
      );
    }
  }

  /// Store FCM token in Firestore with optimized updates
  Future<void> _storeTokenInFirestore(String token) async {
    try {
      // Get current user ID from Firebase Auth
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'No authenticated user, skipping FCM token storage',
        );
        return;
      }

      if (_deviceId == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'Device ID not available, skipping FCM token storage',
        );
        return;
      }

      LoggingService.instance.logInfo(
        _logTag,
        'Checking if FCM token needs update for device: $_deviceId',
      );

      // Get current user to check existing tokens
      final user = await FirestoreService.getUser(currentUser.uid);
      if (user == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'User not found, skipping FCM token storage',
        );
        return;
      }

      final currentPreferences = user.notificationPreferences;
      final existingToken = currentPreferences?.getDeviceToken(_deviceId!);

      // Only update if token is different or device doesn't exist
      if (existingToken != token) {
        LoggingService.instance.logInfo(
          _logTag,
          'Updating FCM token for device $_deviceId: ${token.substring(0, 20)}...',
        );

        await FirestoreService.updateUserFCMToken(
          userId: currentUser.uid,
          deviceId: _deviceId!,
          fcmToken: token,
        );

        LoggingService.instance.logInfo(
          _logTag,
          'Successfully updated FCM token for device $_deviceId',
        );
      } else {
        LoggingService.instance.logInfo(
          _logTag,
          'FCM token unchanged for device $_deviceId, skipping update',
        );
      }
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to store FCM token in Firestore',
      );
    }
  }

  /// Handle token refresh
  void _onTokenRefresh(String token) {
    LoggingService.instance.logInfo(
      _logTag,
      'FCM token refreshed: ${token.substring(0, 20)}...',
    );

    _currentToken = token;
    _tokenController.add(token);

    // Store new token in Firestore
    _storeTokenInFirestore(token);
  }

  /// Set up message handlers for different app states
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen(_onForegroundMessage);

    // Handle messages when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen(_onMessageOpenedApp);

    // Handle messages when app is opened from terminated state
    _handleInitialMessage();
  }

  /// Handle messages when app is in foreground
  void _onForegroundMessage(RemoteMessage message) {
    LoggingService.instance.logInfo(
      _logTag,
      'Received foreground message: ${message.messageId}',
    );

    _messageController.add(message);

    // Show in-app notification or handle as needed
    _showInAppNotification(message);
  }

  /// Handle messages when app is opened from background
  void _onMessageOpenedApp(RemoteMessage message) {
    LoggingService.instance.logInfo(
      _logTag,
      'App opened from background message: ${message.messageId}',
    );

    _messageController.add(message);

    // Show in-app notification for consistency
    _showInAppNotification(message);

    // Handle navigation
    _handleMessageNavigation(message);
  }

  /// Handle initial message when app is opened from terminated state
  Future<void> _handleInitialMessage() async {
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      LoggingService.instance.logInfo(
        _logTag,
        'App opened from terminated state message: ${initialMessage.messageId}',
      );

      _messageController.add(initialMessage);

      // Show in-app notification for consistency
      _showInAppNotification(initialMessage);

      // Handle navigation
      _handleMessageNavigation(initialMessage);
    }
  }

  /// Show in-app notification for foreground messages
  void _showInAppNotification(RemoteMessage message) {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Showing in-app notification: ${message.notification?.title}',
      );

      // Check if we have a valid navigator context
      final context = _navigatorKey?.currentContext;
      if (context == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'No navigator context available for in-app notification',
        );
        return;
      }

      // Show the in-app notification
      InAppNotification.show(
        context,
        message: message,
        onTap: () {
          LoggingService.instance.logInfo(
            _logTag,
            'In-app notification tapped: ${message.messageId}',
          );
          _handleMessageNavigation(message);
        },
        onDismiss: () {
          LoggingService.instance.logInfo(
            _logTag,
            'In-app notification dismissed: ${message.messageId}',
          );
        },
      );
    } catch (e, stackTrace) {
      LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to show in-app notification',
      );
    }
  }

  /// Handle navigation based on message data
  void _handleMessageNavigation(RemoteMessage message) {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Handling navigation for message: ${message.data}',
      );

      // Check if we have a valid navigator context
      final navigator = _navigatorKey?.currentState;
      if (navigator == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'No navigator available for message navigation',
        );
        return;
      }

      // Parse the message data
      final messageType = message.data['messageType'] as String?;
      if (messageType == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'No messageType found in message data',
        );
        return;
      }

      // Handle navigation based on message type
      switch (messageType) {
        case 'engagement':
          _navigateToEngagement(navigator, message);
          break;
        case 'goals_and_progress':
          _navigateToGoalsAndProgress(navigator, message);
          break;
        case 'guided_path_updates':
          _navigateToGuidedPath(navigator, message);
          break;
        case 'system_announcements':
          _navigateToAnnouncements(navigator, message);
          break;
        case 'weekly_insights':
          _navigateToInsights(navigator, message);
          break;
        default:
          LoggingService.instance.logInfo(
            _logTag,
            'Unknown message type for navigation: $messageType',
          );
          // Default to home page
          navigator.pushNamedAndRemoveUntil('/', (route) => false);
      }
    } catch (e, stackTrace) {
      LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to handle message navigation',
      );
    }
  }

  /// Navigate to engagement screen
  void _navigateToEngagement(NavigatorState navigator, RemoteMessage message) {
    LoggingService.instance.logInfo(_logTag, 'Navigating to engagement screen');
    // Navigate to home page for engagement notifications
    navigator.pushNamedAndRemoveUntil('/', (route) => false);
  }

  /// Navigate to goals and progress screen
  void _navigateToGoalsAndProgress(
    NavigatorState navigator,
    RemoteMessage message,
  ) {
    final goalId = message.data['goalId'] as String?;
    LoggingService.instance.logInfo(
      _logTag,
      'Navigating to goals and progress screen, goalId: $goalId',
    );
    // Navigate to home page for now - could be enhanced to specific goal
    navigator.pushNamedAndRemoveUntil('/', (route) => false);
  }

  /// Navigate to guided path screen
  void _navigateToGuidedPath(NavigatorState navigator, RemoteMessage message) {
    final pathId = message.data['pathId'] as String?;
    final stepId = message.data['stepId'] as String?;
    LoggingService.instance.logInfo(
      _logTag,
      'Navigating to guided path screen, pathId: $pathId, stepId: $stepId',
    );
    // Navigate to guided paths page
    navigator.pushNamedAndRemoveUntil('/guided-paths', (route) => false);
  }

  /// Navigate to system announcements screen
  void _navigateToAnnouncements(
    NavigatorState navigator,
    RemoteMessage message,
  ) {
    final announcementId = message.data['announcementId'] as String?;
    LoggingService.instance.logInfo(
      _logTag,
      'Navigating to announcements screen, announcementId: $announcementId',
    );
    // Navigate to home page for announcements
    navigator.pushNamedAndRemoveUntil('/', (route) => false);
  }

  /// Navigate to weekly insights screen
  void _navigateToInsights(NavigatorState navigator, RemoteMessage message) {
    final insightId = message.data['insightId'] as String?;
    LoggingService.instance.logInfo(
      _logTag,
      'Navigating to insights screen, insightId: $insightId',
    );
    // Navigate to home page for insights
    navigator.pushNamedAndRemoveUntil('/', (route) => false);
  }

  /// Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      LoggingService.instance.logInfo(_logTag, 'Subscribing to topic: $topic');

      await FirebaseMessaging.instance.subscribeToTopic(topic);

      LoggingService.instance.logInfo(
        _logTag,
        'Successfully subscribed to topic: $topic',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to subscribe to topic: $topic',
      );
      rethrow;
    }
  }

  /// Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Unsubscribing from topic: $topic',
      );

      await FirebaseMessaging.instance.unsubscribeFromTopic(topic);

      LoggingService.instance.logInfo(
        _logTag,
        'Successfully unsubscribed from topic: $topic',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to unsubscribe from topic: $topic',
      );
      rethrow;
    }
  }

  /// Check current notification permission status
  Future<AuthorizationStatus> getPermissionStatus() async {
    try {
      final settings = await FirebaseMessaging.instance
          .getNotificationSettings();
      return settings.authorizationStatus;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to get permission status',
      );
      return AuthorizationStatus.notDetermined;
    }
  }

  /// Dispose of resources
  void dispose() {
    _messageController.close();
    _tokenController.close();
    _isInitialized = false;
  }
}

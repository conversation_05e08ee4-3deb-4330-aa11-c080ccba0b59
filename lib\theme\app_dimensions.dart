import 'package:flutter/material.dart';

/// Dimension constants and spacing utilities for the Upshift app
/// Provides consistent spacing, sizing, and layout measurements
class AppDimensions {
  // Base spacing unit (8dp grid system)
  static const double baseUnit = 8.0;

  // Spacing values
  static const double spacingXs = baseUnit * 0.5; // 4dp
  static const double spacingS = baseUnit; // 8dp
  static const double spacingM = baseUnit * 2; // 16dp
  static const double spacingL = baseUnit * 3; // 24dp
  static const double spacingXl = baseUnit * 4; // 32dp
  static const double spacingXxl = baseUnit * 6; // 48dp

  // Padding values
  static const EdgeInsets paddingXs = EdgeInsets.all(spacingXs);
  static const EdgeInsets paddingS = EdgeInsets.all(spacingS);
  static const EdgeInsets paddingM = EdgeInsets.all(spacingM);
  static const EdgeInsets paddingL = EdgeInsets.all(spacingL);
  static const EdgeInsets paddingXl = EdgeInsets.all(spacingXl);

  // Horizontal padding
  static const EdgeInsets paddingHorizontalXs = EdgeInsets.symmetric(
    horizontal: spacingXs,
  );
  static const EdgeInsets paddingHorizontalS = EdgeInsets.symmetric(
    horizontal: spacingS,
  );
  static const EdgeInsets paddingHorizontalM = EdgeInsets.symmetric(
    horizontal: spacingM,
  );
  static const EdgeInsets paddingHorizontalL = EdgeInsets.symmetric(
    horizontal: spacingL,
  );
  static const EdgeInsets paddingHorizontalXl = EdgeInsets.symmetric(
    horizontal: spacingXl,
  );

  // Vertical padding
  static const EdgeInsets paddingVerticalXs = EdgeInsets.symmetric(
    vertical: spacingXs,
  );
  static const EdgeInsets paddingVerticalS = EdgeInsets.symmetric(
    vertical: spacingS,
  );
  static const EdgeInsets paddingVerticalM = EdgeInsets.symmetric(
    vertical: spacingM,
  );
  static const EdgeInsets paddingVerticalL = EdgeInsets.symmetric(
    vertical: spacingL,
  );
  static const EdgeInsets paddingVerticalXl = EdgeInsets.symmetric(
    vertical: spacingXl,
  );

  // Border radius values
  static const double radiusXs = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXl = 24.0;
  static const double radiusRound = 50.0; // For circular elements

  // Border radius objects
  static const BorderRadius borderRadiusXs = BorderRadius.all(
    Radius.circular(radiusXs),
  );
  static const BorderRadius borderRadiusS = BorderRadius.all(
    Radius.circular(radiusS),
  );
  static const BorderRadius borderRadiusM = BorderRadius.all(
    Radius.circular(radiusM),
  );
  static const BorderRadius borderRadiusL = BorderRadius.all(
    Radius.circular(radiusL),
  );
  static const BorderRadius borderRadiusXl = BorderRadius.all(
    Radius.circular(radiusXl),
  );

  // Elevation values
  static const double elevationNone = 0.0;
  static const double elevationXs = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXl = 16.0;

  // Icon sizes
  static const double iconXs = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXl = 48.0;
  static const double iconXxl = 64.0;

  // Button dimensions
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXl = 56.0;

  static const double buttonMinWidth = 64.0;
  static const double fabSize = 56.0;
  static const double fabSizeSmall = 40.0;
  static const double fabSizeLarge = 96.0;

  // Input field dimensions
  static const double inputHeight = 48.0;
  static const double inputHeightLarge = 56.0;
  static const double inputBorderWidth = 1.0;
  static const double inputBorderWidthFocused = 2.0;

  // Card dimensions
  static const double cardMinHeight = 80.0;
  static const double cardMaxWidth = 400.0;
  static const double cardElevation = elevationS;

  // List item dimensions
  static const double listItemHeight = 56.0;
  static const double listItemHeightLarge = 72.0;
  static const double listItemHeightSmall = 40.0;

  // App bar dimensions
  static const double appBarHeight = 56.0;
  static const double appBarHeightLarge = 64.0;
  static const double toolbarHeight = 56.0;

  // Bottom navigation dimensions
  static const double bottomNavHeight = 80.0;
  static const double bottomNavIconSize = iconM;

  // Chat-specific dimensions
  static const double chatBubbleMaxWidth = 280.0;
  static const double chatBubbleMinHeight = 40.0;
  static const double chatInputHeight = 48.0;
  static const double chatInputMaxHeight = 120.0;
  static const double chatAvatarSize = 32.0;

  // Progress indicator dimensions
  static const double progressIndicatorHeight = 4.0;
  static const double progressIndicatorHeightLarge = 8.0;
  static const double circularProgressSize = 20.0;
  static const double circularProgressSizeLarge = 32.0;

  // Step indicator dimensions
  static const double stepIndicatorSize = 32.0;
  static const double stepIndicatorSizeLarge = 40.0;
  static const double stepConnectorWidth = 2.0;

  // Avatar dimensions
  static const double avatarXs = 24.0;
  static const double avatarS = 32.0;
  static const double avatarM = 40.0;
  static const double avatarL = 56.0;
  static const double avatarXl = 80.0;
  static const double avatarXxl = 120.0;

  // Breakpoints for responsive design
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  // Safe area minimums
  static const double minTouchTarget = 44.0; // iOS HIG minimum
  static const double minTouchTargetAndroid = 48.0; // Material Design minimum

  // Animation durations (in milliseconds)
  static const int animationDurationFast = 150;
  static const int animationDurationNormal = 300;
  static const int animationDurationSlow = 500;

  // Utility methods

  /// Get responsive padding based on screen width
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < mobileBreakpoint) {
      return paddingM;
    } else if (screenWidth < tabletBreakpoint) {
      return paddingL;
    } else {
      return paddingXl;
    }
  }

  /// Get responsive spacing based on screen width
  static double getResponsiveSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < mobileBreakpoint) {
      return spacingM;
    } else if (screenWidth < tabletBreakpoint) {
      return spacingL;
    } else {
      return spacingXl;
    }
  }

  /// Get responsive card width
  static double getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < mobileBreakpoint) {
      return screenWidth - (spacingM * 2);
    } else {
      return (cardMaxWidth).clamp(300.0, screenWidth - (spacingL * 2));
    }
  }

  /// Get responsive icon size
  static double getResponsiveIconSize(
    BuildContext context, {
    double baseSize = iconM,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < mobileBreakpoint) {
      return baseSize;
    } else if (screenWidth < tabletBreakpoint) {
      return baseSize * 1.1;
    } else {
      return baseSize * 1.2;
    }
  }

  /// Get responsive font size multiplier
  static double getResponsiveFontMultiplier(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < mobileBreakpoint) {
      return 1.0;
    } else if (screenWidth < tabletBreakpoint) {
      return 1.1;
    } else {
      return 1.2;
    }
  }

  /// Check if screen is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if screen is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Get safe minimum touch target size for current platform
  static double getMinTouchTarget(BuildContext context) {
    final platform = Theme.of(context).platform;
    return platform == TargetPlatform.iOS
        ? minTouchTarget
        : minTouchTargetAndroid;
  }

  /// Create consistent box decoration with elevation
  static BoxDecoration createElevatedDecoration({
    required Color color,
    double elevation = elevationS,
    BorderRadius? borderRadius,
    Color? shadowColor,
  }) {
    return BoxDecoration(
      color: color,
      borderRadius: borderRadius ?? borderRadiusM,
      boxShadow: elevation > 0
          ? [
              BoxShadow(
                color: (shadowColor ?? Colors.black).withValues(alpha: 0.1),
                blurRadius: elevation * 2,
                offset: Offset(0, elevation),
              ),
            ]
          : null,
    );
  }

  /// Create consistent border decoration
  static BoxDecoration createBorderDecoration({
    required Color borderColor,
    double borderWidth = 1.0,
    BorderRadius? borderRadius,
    Color? backgroundColor,
  }) {
    return BoxDecoration(
      color: backgroundColor,
      borderRadius: borderRadius ?? borderRadiusM,
      border: Border.all(color: borderColor, width: borderWidth),
    );
  }
}

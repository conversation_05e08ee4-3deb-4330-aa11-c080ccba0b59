// This is a basic Flutter widget test for the Upshift app.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Simple test widget that mimics the BottomNavigationBar structure
class TestBottomNavigationWidget extends StatefulWidget {
  const TestBottomNavigationWidget({super.key});

  @override
  State<TestBottomNavigationWidget> createState() =>
      _TestBottomNavigationWidgetState();
}

class _TestBottomNavigationWidgetState
    extends State<TestBottomNavigationWidget> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: const [
          Center(child: Text('Home Page')),
          Center(child: Text('Chat Page')),
          Center(child: Text('Account Page')),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Chat'),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_circle),
            label: 'Account',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.blue,
        onTap: _onItemTapped,
      ),
    );
  }
}

void main() {
  testWidgets('BottomNavigationBar navigation test', (
    WidgetTester tester,
  ) async {
    // Build the test widget
    await tester.pumpWidget(
      MaterialApp(home: const TestBottomNavigationWidget()),
    );

    // Verify that the BottomNavigationBar is present
    expect(find.byType(BottomNavigationBar), findsOneWidget);

    // Verify that all three tabs are present
    expect(find.text('Home'), findsOneWidget);
    expect(find.text('Chat'), findsOneWidget);
    expect(find.text('Account'), findsOneWidget);

    // Verify that the Home tab is initially selected (index 0)
    final BottomNavigationBar bottomNav = tester.widget(
      find.byType(BottomNavigationBar),
    );
    expect(bottomNav.currentIndex, equals(0));

    // Verify initial page content
    expect(find.text('Home Page'), findsOneWidget);

    // Tap on the Chat tab
    await tester.tap(find.text('Chat'));
    await tester.pump();

    // Verify that the Chat tab is now selected (index 1)
    final BottomNavigationBar updatedBottomNav = tester.widget(
      find.byType(BottomNavigationBar),
    );
    expect(updatedBottomNav.currentIndex, equals(1));

    // Verify page content changed
    expect(find.text('Chat Page'), findsOneWidget);

    // Tap on the Account tab
    await tester.tap(find.text('Account'));
    await tester.pump();

    // Verify that the Account tab is now selected (index 2)
    final BottomNavigationBar finalBottomNav = tester.widget(
      find.byType(BottomNavigationBar),
    );
    expect(finalBottomNav.currentIndex, equals(2));

    // Verify page content changed
    expect(find.text('Account Page'), findsOneWidget);
  });

  testWidgets('BottomNavigationBar icons test', (WidgetTester tester) async {
    // Build the test widget
    await tester.pumpWidget(
      MaterialApp(home: const TestBottomNavigationWidget()),
    );

    // Verify that the correct icons are present
    expect(find.byIcon(Icons.home), findsOneWidget);
    expect(find.byIcon(Icons.chat), findsOneWidget);
    expect(find.byIcon(Icons.account_circle), findsOneWidget);
  });
}

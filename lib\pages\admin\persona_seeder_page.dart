import 'package:flutter/material.dart';
import '../../utils/admin/system_persona_seeder.dart';
import '../../utils/admin/guided_path_seeder_from_json.dart';
import '../../services/firestore.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../../services/logging_service.dart';
import '../../services/analytics_service.dart';
import '../../services/performance_service.dart';
import '../../theme/theme.dart';

/// Admin page for seeding SystemPersona entities and Guided Paths
/// This page provides a UI to seed the default personas and guided paths into Firestore
/// Useful for development, testing, and initial setup
class PersonaSeederPage extends StatefulWidget {
  const PersonaSeederPage({super.key});

  @override
  State<PersonaSeederPage> createState() => _PersonaSeederPageState();
}

class _PersonaSeederPageState extends State<PersonaSeederPage> {
  bool _isLoading = false;
  bool _isCheckingExistence = true;
  bool _personasExist = false;
  bool _guidedPathsExist = false;
  int _personaCount = 0;
  int _guidedPathCount = 0;
  String? _errorMessage;
  List<String>? _createdIds;

  @override
  void initState() {
    super.initState();
    _checkExistence();
  }

  Future<void> _checkExistence() async {
    setState(() {
      _isCheckingExistence = true;
      _errorMessage = null;
    });

    try {
      final results = await Future.wait([
        FirestoreService.getSystemPersonaCount(),
        FirestoreService.getGuidedPathCount(),
      ]);

      final personaCount = results[0];
      final pathCount = results[1];

      setState(() {
        _personaCount = personaCount;
        _personasExist = personaCount > 0;
        _guidedPathCount = pathCount;
        _guidedPathsExist = pathCount > 0;
        _isCheckingExistence = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to check existence: $e';
        _isCheckingExistence = false;
      });
    }
  }

  Future<void> _seedPersonas() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _createdIds = null;
    });

    try {
      final ids = await SystemPersonaSeeder.seedDefaultPersonas();

      setState(() {
        _createdIds = ids;
        _isLoading = false;
      });

      // Refresh the existence check
      await _checkExistence();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully created ${ids.length} personas!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to seed personas: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _seedIfEmpty() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _createdIds = null;
    });

    try {
      final ids = await SystemPersonaSeeder.seedIfEmpty();

      setState(() {
        _createdIds = ids;
        _isLoading = false;
      });

      // Refresh the existence check
      await _checkExistence();

      if (mounted) {
        if (ids.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Personas already exist, no seeding needed.'),
              backgroundColor: AppColors.warning,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully created ${ids.length} personas!'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to seed personas: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _seedGuidedPaths() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await GuidedPathSeederFromJson.seedDefaultPathsWithSteps();

      setState(() {
        _isLoading = false;
      });

      // Refresh the existence check
      await _checkExistence();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Successfully created guided paths! Total paths: $_guidedPathCount',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to seed guided paths: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _seedGuidedPathsIfEmpty() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final pathIds = await GuidedPathSeederFromJson.seedIfEmpty();

      setState(() {
        _isLoading = false;
      });

      // Refresh the existence check
      await _checkExistence();

      if (mounted) {
        if (pathIds.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Guided paths already exist, no seeding needed.'),
              backgroundColor: Colors.orange,
            ),
          );
        } else {
          final totalPaths = pathIds.keys.length;
          final totalSteps = pathIds.values.fold<int>(
            0,
            (sum, steps) => sum + steps.length,
          );
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Successfully created $totalPaths paths with $totalSteps steps!',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to seed guided paths: $e';
        _isLoading = false;
      });
    }
  }

  /// Show confirmation dialog before testing crash
  void _showCrashConfirmationDialog() {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Test Crash'),
          content: const Text(
            'This will force the app to crash for testing Crashlytics. '
            'The app will immediately close. Are you sure?',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Small delay to allow dialog to close
                Future.delayed(const Duration(milliseconds: 500), () {
                  LoggingService.instance.testCrash();
                });
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Crash App'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Seeder'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Seeding Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    if (_isCheckingExistence)
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Checking existing data...'),
                        ],
                      )
                    else ...[
                      // SystemPersona Status
                      Row(
                        children: [
                          Icon(
                            _personasExist ? Icons.check_circle : Icons.warning,
                            color: _personasExist
                                ? Colors.green
                                : Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _personasExist
                                ? 'Personas: $_personaCount found'
                                : 'Personas: None found',
                            style: TextStyle(
                              color: _personasExist
                                  ? Colors.green
                                  : Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Guided Paths Status
                      Row(
                        children: [
                          Icon(
                            _guidedPathsExist
                                ? Icons.check_circle
                                : Icons.warning,
                            color: _guidedPathsExist
                                ? Colors.green
                                : Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _guidedPathsExist
                                ? 'Guided Paths: $_guidedPathCount found'
                                : 'Guided Paths: None found',
                            style: TextStyle(
                              color: _guidedPathsExist
                                  ? Colors.green
                                  : Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _checkExistence,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Refresh Status'),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Seeding Actions',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 16),

                    // SystemPersona Seeding
                    Text(
                      'SystemPersona Seeding',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _seedIfEmpty,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.auto_fix_high),
                      label: const Text('Seed Personas If Empty'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.info,
                        foregroundColor: AppColors.onPrimary,
                      ),
                    ),

                    SizedBox(height: AppDimensions.spacingS),

                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _seedPersonas,
                      icon: _isLoading
                          ? SizedBox(
                              width: AppDimensions.iconS,
                              height: AppDimensions.iconS,
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            )
                          : Icon(AppIcons.add),
                      label: const Text('Force Seed Personas'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.warning,
                        foregroundColor: AppColors.onPrimary,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Guided Paths Seeding
                    Text(
                      'Guided Paths Seeding',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _seedGuidedPathsIfEmpty,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.auto_fix_high),
                      label: const Text('Seed Guided Paths If Empty'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),

                    const SizedBox(height: 8),

                    ElevatedButton.icon(
                      onPressed: _isLoading ? null : _seedGuidedPaths,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.add_circle),
                      label: const Text('Force Seed Guided Paths'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.deepOrange,
                        foregroundColor: Colors.white,
                      ),
                    ),

                    const SizedBox(height: 16),

                    const Text(
                      'Note: "Seed If Empty" will only create data if none exists. '
                      '"Force Seed" will always create new data, potentially creating duplicates.',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Error',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            if (_createdIds != null && _createdIds!.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green.shade700,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Success',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Created ${_createdIds!.length} personas successfully!',
                        style: TextStyle(color: Colors.green.shade700),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            // Crashlytics Testing Section
            const SizedBox(height: 24),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Firebase Monitoring Testing',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      kIsWeb
                          ? 'Test Firebase Analytics and Performance (Crashlytics not available on web)'
                          : 'Test Firebase Crashlytics, Analytics, and Performance integration',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: kIsWeb
                                ? null
                                : () {
                                    LoggingService.instance.testNonFatalError();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          kIsWeb
                                              ? 'Crashlytics not available on web'
                                              : 'Non-fatal error sent to Crashlytics',
                                        ),
                                        backgroundColor: kIsWeb
                                            ? Colors.grey
                                            : Colors.orange,
                                      ),
                                    );
                                  },
                            icon: const Icon(Icons.warning),
                            label: const Text('Test Non-Fatal Error'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: kIsWeb
                                ? null
                                : () {
                                    _showCrashConfirmationDialog();
                                  },
                            icon: const Icon(Icons.error),
                            label: const Text('Test Crash'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Analytics Testing',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              AnalyticsService.instance.logAdminAction(
                                action: 'test_analytics_event',
                                additionalParams: {
                                  'timestamp': DateTime.now().toIso8601String(),
                                },
                              );
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Test event sent to Analytics'),
                                  backgroundColor: Colors.blue,
                                ),
                              );
                            },
                            icon: const Icon(Icons.analytics),
                            label: const Text('Test Analytics Event'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              AnalyticsService.instance.logScreenView(
                                screenName: 'admin_test_screen',
                                screenClass: 'AdminTestPage',
                              );
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Screen view sent to Analytics',
                                  ),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            },
                            icon: const Icon(Icons.screen_share),
                            label: const Text('Test Screen View'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Performance Monitoring Testing',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () async {
                              final scaffoldMessenger = ScaffoldMessenger.of(
                                context,
                              );

                              // Test a simple performance trace
                              await PerformanceService.instance.measureTrace(
                                'admin_test_trace',
                                () async {
                                  // Simulate some work
                                  await Future.delayed(
                                    const Duration(milliseconds: 500),
                                  );
                                },
                                attributes: {'test_type': 'admin_action'},
                                metrics: {'test_duration_ms': 500},
                              );

                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Performance trace completed',
                                    ),
                                    backgroundColor: Colors.purple,
                                  ),
                                );
                              }
                            },
                            icon: const Icon(Icons.speed),
                            label: const Text('Test Performance Trace'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () async {
                              final scaffoldMessenger = ScaffoldMessenger.of(
                                context,
                              );

                              // Test Firestore operation performance
                              await PerformanceService.instance
                                  .measureFirestoreOperation(
                                    () async {
                                      // Get persona count as a test operation
                                      await FirestoreService.getSystemPersonaCount();
                                    },
                                    'count',
                                    'systemPersonas',
                                  );

                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Firestore performance test completed',
                                    ),
                                    backgroundColor: Colors.indigo,
                                  ),
                                );
                              }
                            },
                            icon: const Icon(Icons.storage),
                            label: const Text('Test Firestore Performance'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.indigo,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

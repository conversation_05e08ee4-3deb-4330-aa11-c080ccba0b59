import 'dart:convert';
import 'package:flutter/services.dart';
import '../../models/models.dart';
import '../../services/firestore.dart';

/// Utility class for seeding default guided paths and their steps from JSON file
/// Reads data from data/guide-paths.json instead of using hardcoded data
class GuidedPathSeederFromJson {
  /// Loads and parses the guided paths data from JSON file
  static Future<List<Map<String, dynamic>>> _loadPathsFromJson() async {
    try {
      // Load the JSON file from assets
      final String jsonString = await rootBundle.loadString('data/guide-paths.json');
      
      // Parse the JSON
      final List<dynamic> jsonData = jsonDecode(jsonString);
      
      // Convert to List<Map<String, dynamic>>
      return jsonData.cast<Map<String, dynamic>>();
    } catch (e) {
      throw Exception('Failed to load guided paths from JSON: $e');
    }
  }

  /// Converts JSON data to GuidedPath and PathStep objects
  static Future<List<Map<String, dynamic>>> getDefaultPathsWithSteps() async {
    final jsonData = await _loadPathsFromJson();
    final result = <Map<String, dynamic>>[];
    
    for (final pathData in jsonData) {
      try {
        // Extract path and steps data
        final pathJson = pathData['path'] as Map<String, dynamic>;
        final stepsJson = pathData['steps'] as List<dynamic>;
        
        // Create GuidedPath object from JSON
        final guidedPath = GuidedPath.fromJson(pathJson);
        
        // Create PathStep objects from JSON
        final steps = <PathStep>[];
        for (final stepJson in stepsJson) {
          final stepData = stepJson as Map<String, dynamic>;
          // Set pathId to empty string - will be updated after path creation
          stepData['pathId'] = '';
          final pathStep = PathStep.fromJson(stepData);
          steps.add(pathStep);
        }
        
        // Add to result
        result.add({
          'path': guidedPath,
          'steps': steps,
        });
      } catch (e) {
        throw Exception('Failed to parse path data: $e');
      }
    }
    
    return result;
  }

  /// Seeds the default guided paths and their steps into Firestore
  /// Returns a map with path IDs as keys and lists of step IDs as values
  static Future<Map<String, List<String>>> seedDefaultPathsWithSteps() async {
    final pathsWithSteps = await getDefaultPathsWithSteps();
    final result = <String, List<String>>{};

    for (final pathData in pathsWithSteps) {
      final guidedPath = pathData['path'] as GuidedPath;
      final steps = pathData['steps'] as List<PathStep>;

      // Create the guided path first
      final pathId = await FirestoreService.createGuidedPath(guidedPath);

      // Update steps with the correct pathId and create them
      final updatedSteps = steps
          .map((step) => step.copyWith(pathId: pathId))
          .toList();
      final stepIds = await FirestoreService.createPathSteps(updatedSteps);

      result[pathId] = stepIds;
    }

    return result;
  }

  /// Checks if guided paths already exist in Firestore
  /// Returns true if paths exist, false if the collection is empty
  static Future<bool> pathsExist() async {
    final count = await FirestoreService.getGuidedPathCount();
    return count > 0;
  }

  /// Seeds paths only if they don't already exist
  /// Returns the map of created path and step IDs, or empty map if paths already exist
  static Future<Map<String, List<String>>> seedIfEmpty() async {
    if (await pathsExist()) {
      return {};
    }
    return await seedDefaultPathsWithSteps();
  }

  /// Gets the list of default path categories from JSON data
  static Future<List<String>> getDefaultCategories() async {
    final pathsWithSteps = await getDefaultPathsWithSteps();
    final categories = <String>{};
    
    for (final pathData in pathsWithSteps) {
      final guidedPath = pathData['path'] as GuidedPath;
      categories.add(guidedPath.category);
    }
    
    return categories.toList()..sort();
  }

  /// Gets the starter path for free users from JSON data
  static Future<String?> getStarterPathName() async {
    final pathsWithSteps = await getDefaultPathsWithSteps();
    
    for (final pathData in pathsWithSteps) {
      final guidedPath = pathData['path'] as GuidedPath;
      if (guidedPath.targetUserTier == 'free') {
        return guidedPath.name;
      }
    }
    
    return null; // No free path found
  }

  /// Validates the JSON structure against expected schema
  static Future<bool> validateJsonStructure() async {
    try {
      final pathsWithSteps = await getDefaultPathsWithSteps();
      
      // Basic validation checks
      if (pathsWithSteps.isEmpty) {
        throw Exception('No paths found in JSON data');
      }
      
      // Check that we have all 4 expected categories
      final categories = await getDefaultCategories();
      final expectedCategories = [
        'Focus & Productivity',
        'Mindset & Resilience',
        'Habit Formation',
        'Life Design',
      ];
      
      for (final expectedCategory in expectedCategories) {
        if (!categories.contains(expectedCategory)) {
          throw Exception('Missing expected category: $expectedCategory');
        }
      }
      
      // Check that we have at least one free tier path
      final starterPath = await getStarterPathName();
      if (starterPath == null) {
        throw Exception('No free tier starter path found');
      }
      
      return true;
    } catch (e) {
      throw Exception('JSON validation failed: $e');
    }
  }
}

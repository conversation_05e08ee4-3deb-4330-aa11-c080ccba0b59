import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../services/firestore.dart';
import '../services/subscription_service.dart';
import '../models/models.dart' as models;
import '../widgets/guided_path_card.dart';
import '../theme/theme.dart';
import 'guided_path_detail_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  List<models.GuidedPath> _guidedPaths = [];
  Map<String, models.UserPathProgress> _userProgress = {};
  models.User? _currentUser;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadGuidedPaths();
  }

  Future<void> _loadGuidedPaths() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        setState(() {
          _errorMessage = 'User not authenticated';
          _isLoading = false;
        });
        return;
      }

      // Load user data, ALL guided paths, and user progress in parallel
      final results = await Future.wait([
        FirestoreService.getUser(currentUser.uid),
        FirestoreService.getActiveGuidedPaths(), // Changed to load all paths
        FirestoreService.getUserAllPathProgress(currentUser.uid),
      ]);

      final user = results[0] as models.User?;
      final guidedPaths = results[1] as List<models.GuidedPath>;
      final userProgressList = results[2] as List<models.UserPathProgress>;

      // Create a map of pathId to progress for easy lookup
      final progressMap = <String, models.UserPathProgress>{};
      for (final progress in userProgressList) {
        progressMap[progress.pathId] = progress;
      }

      setState(() {
        _currentUser = user;
        _guidedPaths = guidedPaths;
        _userProgress = progressMap;
        _isLoading = false;
      });
    } catch (e) {
      final errorMsg = 'Failed to load guided paths: $e';
      debugPrint('Error loading guided paths: $errorMsg');
      setState(() {
        _errorMessage = errorMsg;
        _isLoading = false;
      });
    }
  }

  bool _isUserAccessibleToPath(models.GuidedPath path) {
    // Free tier paths are accessible to everyone
    if (path.targetUserTier == 'free') return true;

    // Paid tier paths require premium access or admin privileges
    return _hasUserPremiumAccess();
  }

  bool _hasUserPremiumAccess() {
    // Admin users get premium access
    if (_currentUser?.isAdmin == true) return true;

    // Check subscription service for premium access
    try {
      if (SubscriptionService.instance.isInitialized) {
        return SubscriptionService
            .instance
            .currentSubscription
            .hasPremiumAccess;
      }
    } catch (e) {
      debugPrint('Error checking subscription status: $e');
    }

    // Default to free access if subscription service is not available
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Guided Coaching Paths'),
        actions: [
          IconButton(icon: Icon(AppIcons.refresh), onPressed: _loadGuidedPaths),
          IconButton(
            icon: Icon(AppIcons.close),
            onPressed: () => FirebaseAuth.instance.signOut(),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.error,
              size: AppDimensions.iconXxl,
              color: context.colorScheme.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              _errorMessage!,
              style: context.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            ElevatedButton(
              onPressed: _loadGuidedPaths,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_guidedPaths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.paths,
              size: AppDimensions.iconXxl,
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'No guided paths available',
              style: context.textTheme.headlineSmall,
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Check back later for new coaching paths',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return _buildPathsList();
  }

  Widget _buildPathsList() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress overview
          if (_userProgress.isNotEmpty) _buildProgressOverview(),

          // All guided paths in a flat list
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'All Guided Paths',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),

          // Display all paths without category grouping
          ..._guidedPaths.map(
            (path) => GuidedPathCard(
              guidedPath: path,
              userProgress: _userProgress[path.id],
              isAccessible: _isUserAccessibleToPath(path),
              onTap: () => _navigateToPathDetail(path),
              onContinue: () => _navigateToPathDetail(path),
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildProgressOverview() {
    final totalPaths = _guidedPaths.length;
    final startedPaths = _userProgress.length;
    final completedPaths = _userProgress.values
        .where((p) => p.isCompleted)
        .length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Progress',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildProgressStat(
                'Started',
                startedPaths,
                totalPaths,
                AppColors.info,
              ),
              SizedBox(width: AppDimensions.spacingM),
              _buildProgressStat(
                'Completed',
                completedPaths,
                totalPaths,
                AppColors.success,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressStat(String label, int value, int total, Color color) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: context.textTheme.labelMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: AppDimensions.spacingXs),
          Row(
            children: [
              Text(
                '$value',
                style: context.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                ' / $total',
                style: context.textTheme.titleMedium?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToPathDetail(models.GuidedPath path) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => GuidedPathDetailPage(pathId: path.id!),
          ),
        )
        .then((_) => _loadGuidedPaths()); // Refresh when returning
  }
}

const Ajv = require("ajv");
const addFormats = require("ajv-formats");
const fs = require("fs");

const schema = JSON.parse(fs.readFileSync("assets/schemas/system_personas.schema.json", "utf-8"));
const data = JSON.parse(fs.readFileSync("data/system_personas.json", "utf-8"));

const ajv = new Ajv();
addFormats(ajv); // Enables "date-time", "email", etc.

const validate = ajv.compile(schema);
const valid = validate(data);

if (valid) {
  console.log("✅ Validation passed");
} else {
  console.error("❌ Validation failed:", validate.errors);
  process.exit(1);
}

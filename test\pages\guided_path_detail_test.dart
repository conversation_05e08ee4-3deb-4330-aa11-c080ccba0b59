import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart' as models;

void main() {
  group('Step Locking Logic Tests', () {
    late models.User adminUser;
    late models.User regularUser;
    late models.PathStep step1;
    late models.PathStep step2;
    late models.PathStep step3;

    setUp(() {
      final now = DateTime.now();

      adminUser = models.User(
        id: 'admin-user',
        email: '<EMAIL>',
        name: 'Admin User',
        isAdmin: true,
        createdAt: now,
      );

      regularUser = models.User(
        id: 'regular-user',
        email: '<EMAIL>',
        name: 'Regular User',
        isAdmin: false,
        createdAt: now,
      );

      step1 = models.PathStep(
        id: 'step-1',
        pathId: 'test-path',
        stepNumber: 1,
        title: 'Step 1',
        description: 'First step',
        completionCriteria: 'Complete step 1',
        createdAt: now,
      );

      step2 = models.PathStep(
        id: 'step-2',
        pathId: 'test-path',
        stepNumber: 2,
        title: 'Step 2',
        description: 'Second step',
        completionCriteria: 'Complete step 2',
        createdAt: now,
      );

      step3 = models.PathStep(
        id: 'step-3',
        pathId: 'test-path',
        stepNumber: 3,
        title: 'Step 3',
        description: 'Third step',
        completionCriteria: 'Complete step 3',
        createdAt: now,
      );
    });

    test('Admin user should access any step regardless of progress', () {
      // Test with no progress
      expect(_isStepLocked(step1, null, adminUser), false);
      expect(_isStepLocked(step2, null, adminUser), false);
      expect(_isStepLocked(step3, null, adminUser), false);

      // Test with some progress
      final progress = models.UserPathProgress(
        userId: 'admin-user',
        pathId: 'test-path',
        currentStepNumber: 1,
        completedSteps: const [],
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
      );

      expect(_isStepLocked(step1, progress, adminUser), false);
      expect(_isStepLocked(step2, progress, adminUser), false);
      expect(_isStepLocked(step3, progress, adminUser), false);
    });

    test('Regular user with no progress should only access first step', () {
      expect(_isStepLocked(step1, null, regularUser), false);
      expect(_isStepLocked(step2, null, regularUser), true);
      expect(_isStepLocked(step3, null, regularUser), true);
    });

    test('Regular user should access steps based on progress', () {
      // User on step 1
      final progressStep1 = models.UserPathProgress(
        userId: 'regular-user',
        pathId: 'test-path',
        currentStepNumber: 1,
        completedSteps: const [],
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
      );

      expect(
        _isStepLocked(step1, progressStep1, regularUser),
        false,
      ); // Current step
      expect(
        _isStepLocked(step2, progressStep1, regularUser),
        true,
      ); // Future step
      expect(
        _isStepLocked(step3, progressStep1, regularUser),
        true,
      ); // Future step

      // User on step 2, completed step 1
      final progressStep2 = models.UserPathProgress(
        userId: 'regular-user',
        pathId: 'test-path',
        currentStepNumber: 2,
        completedSteps: const [1],
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
      );

      expect(
        _isStepLocked(step1, progressStep2, regularUser),
        false,
      ); // Completed step
      expect(
        _isStepLocked(step2, progressStep2, regularUser),
        false,
      ); // Current step
      expect(
        _isStepLocked(step3, progressStep2, regularUser),
        true,
      ); // Future step

      // User completed all steps
      final progressCompleted = models.UserPathProgress(
        userId: 'regular-user',
        pathId: 'test-path',
        currentStepNumber: 3,
        completedSteps: const [1, 2, 3],
        status: 'completed',
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
        completionDate: DateTime.now(),
      );

      expect(
        _isStepLocked(step1, progressCompleted, regularUser),
        false,
      ); // Completed step
      expect(
        _isStepLocked(step2, progressCompleted, regularUser),
        false,
      ); // Completed step
      expect(
        _isStepLocked(step3, progressCompleted, regularUser),
        false,
      ); // Completed step
    });
  });
}

/// Helper function that mimics the logic from GuidedPathDetailPage
bool _isStepLocked(
  models.PathStep step,
  models.UserPathProgress? userProgress,
  models.User? currentUser,
) {
  // Admin users can access any step
  if (currentUser?.isAdmin == true) {
    return false;
  }

  // If no progress exists, only the first step should be accessible
  if (userProgress == null) {
    return step.stepNumber != 1;
  }

  // If step is already completed, it's not locked
  if (userProgress.isStepCompleted(step.stepNumber)) {
    return false;
  }

  // If it's the current step, it's not locked
  if (userProgress.currentStepNumber == step.stepNumber) {
    return false;
  }

  // If it's a future step that hasn't been unlocked yet, it's locked
  return userProgress.currentStepNumber < step.stepNumber;
}

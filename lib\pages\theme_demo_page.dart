import 'package:flutter/material.dart';
import 'package:upshift/theme/theme.dart';
import 'package:upshift/services/theme_service.dart';

/// Demo page to showcase the new dark theme with yellow accents
class ThemeDemoPage extends StatefulWidget {
  const ThemeDemoPage({super.key});

  @override
  State<ThemeDemoPage> createState() => _ThemeDemoPageState();
}

class _ThemeDemoPageState extends State<ThemeDemoPage> {
  late final ThemeService _themeService;
  ThemeMode _currentThemeMode = ThemeMode.system;

  @override
  void initState() {
    super.initState();
    _themeService = ThemeService.instance;
    _currentThemeMode = _themeService.currentTheme;

    // Listen to theme changes
    _themeService.themeStream.listen((themeMode) {
      if (mounted) {
        setState(() {
          _currentThemeMode = themeMode;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Demo'),
        actions: [
          IconButton(
            icon: Icon(_getThemeIcon()),
            onPressed: () => _themeService.toggleTheme(),
            tooltip: 'Toggle Theme',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Theme: ${_getThemeName()}',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Dark theme with vibrant yellow/amber accents and rounded card styling',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Color Showcase
            Text(
              'Color Palette',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _ColorChip(
                  color: Theme.of(context).colorScheme.primary,
                  label: 'Primary',
                ),
                _ColorChip(
                  color: Theme.of(context).colorScheme.secondary,
                  label: 'Secondary',
                ),
                _ColorChip(
                  color: AppColors.focusProductivity,
                  label: 'Focus & Productivity',
                ),
                _ColorChip(
                  color: AppColors.mindsetResilience,
                  label: 'Mindset & Resilience',
                ),
                _ColorChip(
                  color: AppColors.habitFormation,
                  label: 'Habit Formation',
                ),
                _ColorChip(
                  color: AppColors.lifeDesign,
                  label: 'Life Design',
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Button Showcase
            Text(
              'Buttons',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Elevated Button'),
                ),
                OutlinedButton(
                  onPressed: () {},
                  child: const Text('Outlined Button'),
                ),
                TextButton(
                  onPressed: () {},
                  child: const Text('Text Button'),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Card Examples
            Text(
              'Card Examples',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Card(
              child: ListTile(
                leading: const Icon(Icons.person),
                title: const Text('User Profile'),
                subtitle: const Text('Manage your account settings'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {},
              ),
            ),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Progress Card',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: 0.7,
                      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                    ),
                    const SizedBox(height: 8),
                    const Text('70% Complete'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Input Field Example
            Text(
              'Input Fields',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Enter your message',
                hintText: 'Type something...',
                prefixIcon: Icon(Icons.message),
              ),
            ),
            const SizedBox(height: 16),

            // Chip Examples
            Text(
              'Chips',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                Chip(
                  label: const Text('Beginner'),
                  backgroundColor: AppColors.getDifficultyColor('beginner'),
                ),
                Chip(
                  label: const Text('Intermediate'),
                  backgroundColor: AppColors.getDifficultyColor('intermediate'),
                ),
                Chip(
                  label: const Text('Advanced'),
                  backgroundColor: AppColors.getDifficultyColor('advanced'),
                ),
              ],
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {},
        child: const Icon(Icons.add),
      ),
    );
  }

  IconData _getThemeIcon() {
    switch (_currentThemeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.auto_mode;
    }
  }

  String _getThemeName() {
    switch (_currentThemeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }
}

class _ColorChip extends StatelessWidget {
  final Color color;
  final String label;

  const _ColorChip({
    required this.color,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: _getContrastColor(color),
          fontWeight: FontWeight.w500,
          fontSize: 12,
        ),
      ),
    );
  }

  Color _getContrastColor(Color color) {
    // Calculate luminance to determine if we should use black or white text
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}

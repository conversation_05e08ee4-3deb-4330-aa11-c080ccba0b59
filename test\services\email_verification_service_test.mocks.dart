// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in upshift/test/services/email_verification_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:upshift/services/email_verification_interface.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [EmailVerificationInterface].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockEmailVerificationInterface extends _i1.Mock
    implements _i2.EmailVerificationInterface {
  MockEmailVerificationInterface() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<bool> get verificationStatusStream =>
      (super.noSuchMethod(
            Invocation.getter(#verificationStatusStream),
            returnValue: _i3.Stream<bool>.empty(),
          )
          as _i3.Stream<bool>);

  @override
  _i3.Future<bool> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  bool isEmailVerified() =>
      (super.noSuchMethod(
            Invocation.method(#isEmailVerified, []),
            returnValue: false,
          )
          as bool);

  @override
  _i3.Future<bool> reloadUserAndCheckVerification() =>
      (super.noSuchMethod(
            Invocation.method(#reloadUserAndCheckVerification, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  bool needsEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#needsEmailVerification, []),
            returnValue: false,
          )
          as bool);

  @override
  _i3.Future<bool> resendVerificationEmail() =>
      (super.noSuchMethod(
            Invocation.method(#resendVerificationEmail, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);

  @override
  _i3.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  bool isRecentRegistration() =>
      (super.noSuchMethod(
            Invocation.method(#isRecentRegistration, []),
            returnValue: false,
          )
          as bool);
}

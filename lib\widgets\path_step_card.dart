import 'package:flutter/material.dart';
import '../models/models.dart';
import '../theme/theme.dart';

class PathStepCard extends StatelessWidget {
  final PathStep pathStep;
  final UserPathProgress? userProgress;
  final bool isCurrentStep;
  final bool isCompleted;
  final bool isLocked;
  final VoidCallback? onTap;
  final VoidCallback? onComplete;

  const PathStepCard({
    super.key,
    required this.pathStep,
    this.userProgress,
    this.isCurrentStep = false,
    this.isCompleted = false,
    this.isLocked = false,
    this.onTap,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: AppDimensions.paddingHorizontalM.add(
        const EdgeInsets.symmetric(vertical: 6),
      ),
      elevation: isCurrentStep
          ? AppDimensions.elevationM
          : AppDimensions.elevationXs,
      child: InkWell(
        onTap: isLocked ? null : onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isCurrentStep
                ? Border.all(color: colorScheme.primary, width: 2)
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Step number and status indicator
                _buildStepIndicator(context),

                const SizedBox(width: 16),

                // Step content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        pathStep.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isLocked
                              ? colorScheme.onSurface.withValues(alpha: 0.5)
                              : colorScheme.onSurface,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Description
                      Text(
                        pathStep.description,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isLocked
                              ? colorScheme.onSurface.withValues(alpha: 0.4)
                              : colorScheme.onSurface.withValues(alpha: 0.8),
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 12),

                      // Completion criteria
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceContainerHighest.withValues(
                            alpha: 0.5,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.task_alt,
                              size: 16,
                              color: colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                pathStep.completionCriteria,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: isLocked
                                      ? colorScheme.onSurface.withValues(
                                          alpha: 0.4,
                                        )
                                      : colorScheme.onSurface.withValues(
                                          alpha: 0.7,
                                        ),
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 12),

                      // Footer with metadata and actions
                      Row(
                        children: [
                          // Duration
                          if (pathStep.estimatedDurationMinutes != null)
                            _buildInfoChip(
                              context,
                              Icons.schedule,
                              '${pathStep.estimatedDurationMinutes}m',
                            ),

                          // Resources count
                          if (pathStep.resources?.isNotEmpty == true) ...[
                            const SizedBox(width: 8),
                            _buildInfoChip(
                              context,
                              Icons.link,
                              '${pathStep.resources!.length} resources',
                            ),
                          ],

                          // Reflection prompts count
                          if (pathStep.reflectionPrompts?.isNotEmpty ==
                              true) ...[
                            const SizedBox(width: 8),
                            _buildInfoChip(
                              context,
                              Icons.psychology,
                              '${pathStep.reflectionPrompts!.length} prompts',
                            ),
                          ],

                          const Spacer(),

                          // Action button
                          if (!isLocked) _buildActionButton(context),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Widget icon;
    Color backgroundColor;
    Color foregroundColor;

    if (isLocked) {
      icon = Icon(Icons.lock, size: 16);
      backgroundColor = colorScheme.onSurface.withValues(alpha: 0.2);
      foregroundColor = colorScheme.onSurface.withValues(alpha: 0.5);
    } else if (isCompleted) {
      icon = Icon(AppIcons.completed, size: AppDimensions.iconS);
      backgroundColor = AppColors.success;
      foregroundColor = AppColors.onPrimary;
    } else if (isCurrentStep) {
      icon = Text(
        '${pathStep.stepNumber}',
        style: theme.textTheme.labelMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      );
      backgroundColor = colorScheme.primary;
      foregroundColor = colorScheme.onPrimary;
    } else {
      icon = Text('${pathStep.stepNumber}', style: theme.textTheme.labelMedium);
      backgroundColor = colorScheme.surfaceContainerHighest;
      foregroundColor = colorScheme.onSurface.withValues(alpha: 0.7);
    }

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(color: backgroundColor, shape: BoxShape.circle),
      child: Center(
        child: DefaultTextStyle(
          style: TextStyle(color: foregroundColor),
          child: icon,
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context, IconData icon, String label) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(width: 3),
          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (isCompleted) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.success.withValues(alpha: 0.1),
          borderRadius: AppDimensions.borderRadiusXs,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(AppIcons.completed, size: 14, color: AppColors.success),
            SizedBox(width: AppDimensions.spacingXs),
            Text(
              'Completed',
              style: theme.textTheme.labelSmall?.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    if (isCurrentStep) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (onComplete != null) ...[
            TextButton(
              onPressed: onComplete,
              style: TextButton.styleFrom(
                foregroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                'Complete',
                style: theme.textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(width: 4),
          ],
          Icon(Icons.chevron_right, color: colorScheme.primary, size: 20),
        ],
      );
    }

    return Icon(
      Icons.chevron_right,
      color: colorScheme.onSurface.withValues(alpha: 0.4),
      size: 20,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:upshift/services/theme_service.dart';

void main() {
  group('ThemeService', () {
    late ThemeService themeService;

    setUp(() async {
      // Clear shared preferences before each test
      SharedPreferences.setMockInitialValues({});
      themeService = ThemeService.instance;
      await themeService.initialize();
    });

    test('should initialize with system theme by default', () {
      expect(themeService.currentTheme, ThemeMode.system);
    });

    test('should persist theme changes', () async {
      // Change to light theme
      await themeService.setTheme(ThemeMode.light);
      expect(themeService.currentTheme, ThemeMode.light);

      // Create new instance to test persistence
      final newService = ThemeService.instance;
      await newService.initialize();
      expect(newService.currentTheme, ThemeMode.light);
    });

    test('should emit theme changes through stream', () async {
      final themeChanges = <ThemeMode>[];
      themeService.themeStream.listen((theme) {
        themeChanges.add(theme);
      });

      await themeService.setTheme(ThemeMode.dark);
      await themeService.setTheme(ThemeMode.light);

      // Wait for stream events
      await Future.delayed(const Duration(milliseconds: 10));

      expect(themeChanges, contains(ThemeMode.dark));
      expect(themeChanges, contains(ThemeMode.light));
    });

    test('should provide correct display names', () {
      expect(themeService.getThemeDisplayName(ThemeMode.light), 'Light');
      expect(themeService.getThemeDisplayName(ThemeMode.dark), 'Dark');
      expect(themeService.getThemeDisplayName(ThemeMode.system), 'System');
    });

    test('should provide correct icons', () {
      expect(themeService.getThemeIcon(ThemeMode.light), Icons.light_mode);
      expect(themeService.getThemeIcon(ThemeMode.dark), Icons.dark_mode);
      expect(themeService.getThemeIcon(ThemeMode.system), Icons.brightness_auto);
    });

    test('should toggle themes correctly', () async {
      // Start with system
      expect(themeService.currentTheme, ThemeMode.system);

      // Toggle to light
      await themeService.toggleTheme();
      expect(themeService.currentTheme, ThemeMode.light);

      // Toggle to dark
      await themeService.toggleTheme();
      expect(themeService.currentTheme, ThemeMode.dark);

      // Toggle back to system
      await themeService.toggleTheme();
      expect(themeService.currentTheme, ThemeMode.system);
    });

    test('should not emit duplicate theme changes', () async {
      final themeChanges = <ThemeMode>[];
      themeService.themeStream.listen((theme) {
        themeChanges.add(theme);
      });

      // Set same theme multiple times
      await themeService.setTheme(ThemeMode.dark);
      await themeService.setTheme(ThemeMode.dark);
      await themeService.setTheme(ThemeMode.dark);

      // Wait for stream events
      await Future.delayed(const Duration(milliseconds: 10));

      // Should only emit once
      expect(themeChanges.where((theme) => theme == ThemeMode.dark).length, 1);
    });
  });
}

// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in upshift/test/models/cloud_message_models_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:firebase_messaging_platform_interface/firebase_messaging_platform_interface.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [RemoteMessage].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockRemoteMessage extends _i1.Mock implements _i2.RemoteMessage {
  MockRemoteMessage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get contentAvailable =>
      (super.noSuchMethod(
            Invocation.getter(#contentAvailable),
            returnValue: false,
          )
          as bool);

  @override
  Map<String, dynamic> get data =>
      (super.noSuchMethod(
            Invocation.getter(#data),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  bool get mutableContent =>
      (super.noSuchMethod(
            Invocation.getter(#mutableContent),
            returnValue: false,
          )
          as bool);

  @override
  Map<String, dynamic> toMap() =>
      (super.noSuchMethod(
            Invocation.method(#toMap, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);
}

/// A class which mocks [RemoteNotification].
///
/// See the documentation for Mockito's code generation for more information.
class MockRemoteNotification extends _i1.Mock
    implements _i2.RemoteNotification {
  MockRemoteNotification() {
    _i1.throwOnMissingStub(this);
  }

  @override
  List<String> get titleLocArgs =>
      (super.noSuchMethod(
            Invocation.getter(#titleLocArgs),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  List<String> get bodyLocArgs =>
      (super.noSuchMethod(
            Invocation.getter(#bodyLocArgs),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  Map<String, dynamic> toMap() =>
      (super.noSuchMethod(
            Invocation.method(#toMap, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);
}

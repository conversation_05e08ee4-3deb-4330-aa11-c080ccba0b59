// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
// / await Firebase.initializeApp(
// /   options: DefaultFirebaseOptions.currentPlatform,
// / );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCxuuwtfbJI9rS5ZUGj8QwZGpK4M-arP8I',
    appId: '1:49879574938:ios:a402673a16e788e7d67223',
    messagingSenderId: '49879574938',
    projectId: 'upshift-life',
    storageBucket: 'upshift-life.firebasestorage.app',
    iosBundleId: 'life.upshift.upshift',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDi-SaGZjgyJAboY8Eh4kSXWZlzsvPkAvo',
    appId: '1:49879574938:web:7f96a3195d2e7b5ed67223',
    messagingSenderId: '49879574938',
    projectId: 'upshift-life',
    authDomain: 'upshift-life.firebaseapp.com',
    storageBucket: 'upshift-life.firebasestorage.app',
    measurementId: 'G-BZ7DVF41QD',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDi-SaGZjgyJAboY8Eh4kSXWZlzsvPkAvo',
    appId: '1:49879574938:web:7f96a3195d2e7b5ed67223',
    messagingSenderId: '49879574938',
    projectId: 'upshift-life',
    authDomain: 'upshift-life.firebaseapp.com',
    storageBucket: 'upshift-life.firebasestorage.app',
    measurementId: 'G-BZ7DVF41QD',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCxuuwtfbJI9rS5ZUGj8QwZGpK4M-arP8I',
    appId: '1:49879574938:ios:a402673a16e788e7d67223',
    messagingSenderId: '49879574938',
    projectId: 'upshift-life',
    storageBucket: 'upshift-life.firebasestorage.app',
    iosBundleId: 'life.upshift.upshift',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAxpc7riwe41mDHmEe41uzGn1eRssUPP6Q',
    appId: '1:49879574938:android:3335ca6a51c869d6d67223',
    messagingSenderId: '49879574938',
    projectId: 'upshift-life',
    storageBucket: 'upshift-life.firebasestorage.app',
  );
}

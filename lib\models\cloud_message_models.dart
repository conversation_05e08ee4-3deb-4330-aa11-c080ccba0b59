import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:json_annotation/json_annotation.dart';

part 'cloud_message_models.g.dart';

/// Base class for all push notification data
abstract class PushNotificationData {
  const PushNotificationData();

  /// Factory constructor for creating instances from message type
  factory PushNotificationData.fromMessageType(
    String messageType,
    Map<String, dynamic> data,
  ) {
    switch (messageType) {
      case 'engagement':
        return EngagementData.fromJson(data);
      case 'goals_and_progress':
        return GoalsAndProgressData.fromJson(data);
      case 'guided_path_updates':
        return GuidedPathUpdatesData.fromJson(data);
      case 'system_announcements':
        return SystemAnnouncementsData.fromJson(data);
      case 'weekly_insights':
        return WeeklyInsightsData.fromJson(data);
      default:
        throw ArgumentError('Unknown message type: $messageType');
    }
  }

  /// Factory constructor for parsing FCM RemoteMessage
  factory PushNotificationData.fromRemoteMessage(RemoteMessage message) {
    final messageType = message.data['messageType'] as String?;
    if (messageType == null) {
      throw ArgumentError('Missing messageType in RemoteMessage data');
    }

    // Extract the data payload, excluding messageType
    final data = Map<String, dynamic>.from(message.data);
    data.remove('messageType');

    return PushNotificationData.fromMessageType(messageType, data);
  }

  /// Get the message type for this notification data
  String get messageType;

  /// Convert to JSON
  Map<String, dynamic> toJson();
}

/// Engagement notification data
@JsonSerializable()
class EngagementData extends PushNotificationData {
  final String title;
  final String description;
  final String? imageUrl;

  const EngagementData({
    required this.title,
    required this.description,
    this.imageUrl,
  });

  @override
  String get messageType => 'engagement';

  factory EngagementData.fromJson(Map<String, dynamic> json) =>
      _$EngagementDataFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EngagementDataToJson(this);
}

/// Goals and progress tracking notification data
@JsonSerializable()
class GoalsAndProgressData extends PushNotificationData {
  final String goalName;
  final String goalId;
  final String description;
  final int progressPercentage;
  final String currentStep;

  const GoalsAndProgressData({
    required this.goalName,
    required this.goalId,
    required this.description,
    required this.progressPercentage,
    required this.currentStep,
  });

  @override
  String get messageType => 'goals_and_progress';

  factory GoalsAndProgressData.fromJson(Map<String, dynamic> json) =>
      _$GoalsAndProgressDataFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$GoalsAndProgressDataToJson(this);
}

/// Guided path updates notification data
@JsonSerializable()
class GuidedPathUpdatesData extends PushNotificationData {
  final String pathId;
  final String pathName;
  final String stepId;
  final String stepName;
  final String? imageUrl;

  const GuidedPathUpdatesData({
    required this.pathId,
    required this.pathName,
    required this.stepId,
    required this.stepName,
    this.imageUrl,
  });

  @override
  String get messageType => 'guided_path_updates';

  factory GuidedPathUpdatesData.fromJson(Map<String, dynamic> json) =>
      _$GuidedPathUpdatesDataFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$GuidedPathUpdatesDataToJson(this);
}

/// System announcements notification data
@JsonSerializable()
class SystemAnnouncementsData extends PushNotificationData {
  final String announcementId;
  final String announcementTitle;
  final String announcementBody;
  final String? imageUrl;

  const SystemAnnouncementsData({
    required this.announcementId,
    required this.announcementTitle,
    required this.announcementBody,
    this.imageUrl,
  });

  @override
  String get messageType => 'system_announcements';

  factory SystemAnnouncementsData.fromJson(Map<String, dynamic> json) =>
      _$SystemAnnouncementsDataFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SystemAnnouncementsDataToJson(this);
}

/// Weekly insights notification data
@JsonSerializable()
class WeeklyInsightsData extends PushNotificationData {
  final String insightId;
  final String insightTitle;
  final String insightBody;
  final String? imageUrl;

  const WeeklyInsightsData({
    required this.insightId,
    required this.insightTitle,
    required this.insightBody,
    this.imageUrl,
  });

  @override
  String get messageType => 'weekly_insights';

  factory WeeklyInsightsData.fromJson(Map<String, dynamic> json) =>
      _$WeeklyInsightsDataFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$WeeklyInsightsDataToJson(this);
}

/// Complete cloud message wrapper with notification and data
/// Note: This class doesn't support JSON serialization due to the union type
class CloudMessage {
  final String messageType;
  final PushNotificationData data;
  final Map<String, String>? additionalData;

  const CloudMessage({
    required this.messageType,
    required this.data,
    this.additionalData,
  });

  /// Factory constructor for parsing FCM RemoteMessage
  factory CloudMessage.fromRemoteMessage(RemoteMessage message) {
    final messageType = message.data['messageType'] as String?;
    if (messageType == null) {
      throw ArgumentError('Missing messageType in RemoteMessage data');
    }

    final data = PushNotificationData.fromRemoteMessage(message);

    // Extract any additional data that's not part of the specific message type
    final additionalData = Map<String, String>.from(message.data);
    additionalData.remove('messageType');

    return CloudMessage(
      messageType: messageType,
      data: data,
      additionalData: additionalData.isNotEmpty ? additionalData : null,
    );
  }
}

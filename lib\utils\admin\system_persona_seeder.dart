import '../../models/models.dart';
import '../../services/firestore.dart';

/// Utility class for seeding SystemPersona entities
/// Contains the predefined persona data and methods to seed them into Firestore
class SystemPersonaSeeder {
  /// Returns the list of 10 predefined SystemPersona entities
  static List<SystemPersona> getDefaultPersonas() {
    return [
      SystemPersona(
        name: 'The Motivator',
        description:
            'An uplifting and encouraging coach who believes in your potential and helps you see the bright side of every challenge. Specializes in building confidence and maintaining momentum through positive reinforcement and celebration of small wins.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'encouraging',
        specialties: ['confidence-building', 'motivation', 'positive-mindset'],
        approach: 'supportive',
        videoUrl: 'assets/persona-videos/MotivatorVideo.mp4',
      ),

      SystemPersona(
        name: 'The Provocateur',
        description:
            'A bold and challenging coach who pushes you out of your comfort zone and questions your assumptions. Uses tough love and direct confrontation to break through mental barriers and drive breakthrough thinking.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'challenging',
        specialties: [
          'breakthrough-thinking',
          'comfort-zone-expansion',
          'assumption-challenging',
        ],
        approach: 'confrontational',
        videoUrl: 'assets/persona-videos/ProvocateurVideo.mp4',
      ),

      SystemPersona(
        name: 'The Strategist',
        description:
            'An analytical and methodical coach focused on systematic planning and strategic thinking. Helps you break down complex goals into actionable steps and develop comprehensive roadmaps for success.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'analytical',
        specialties: [
          'strategic-planning',
          'goal-decomposition',
          'systematic-thinking',
        ],
        approach: 'methodical',
        videoUrl: 'assets/persona-videos/StrategistVideo.mp4',
      ),

      SystemPersona(
        name: 'The Dark Voice',
        description:
            'A brutally honest coach who addresses harsh truths and helps you confront difficult realities. Specializes in cutting through self-deception and preparing you for worst-case scenarios with unflinching realism.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'realistic',
        specialties: ['truth-telling', 'reality-checking', 'risk-assessment'],
        approach: 'brutally-honest',
        videoUrl: 'assets/persona-videos/DarkVoiceVideo.mp4',
      ),

      SystemPersona(
        name: 'The Saint',
        description:
            'A compassionate and nurturing coach who provides emotional support and gentle guidance. Creates a safe space for vulnerability and healing while helping you develop self-compassion and inner peace.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'nurturing',
        specialties: ['emotional-support', 'self-compassion', 'healing'],
        approach: 'gentle',
        videoUrl: 'assets/persona-videos/SaintVideo.mp4',
      ),

      SystemPersona(
        name: 'The Innovator',
        description:
            'A creative and forward-thinking coach who encourages experimentation and unconventional solutions. Helps you think outside the box and embrace failure as a learning opportunity while fostering innovation.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'creative',
        specialties: ['innovation', 'creative-thinking', 'experimentation'],
        approach: 'experimental',
        videoUrl: 'assets/persona-videos/InnovatorVideo.mp4',
      ),

      SystemPersona(
        name: 'The Warrior',
        description:
            'A fierce and determined coach who instills discipline and mental toughness. Focuses on building resilience, overcoming obstacles, and developing the warrior mindset needed to conquer any challenge.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'disciplined',
        specialties: ['resilience', 'mental-toughness', 'obstacle-overcoming'],
        approach: 'fierce',
        videoUrl: 'assets/persona-videos/WarriorVideo.mp4',
      ),

      SystemPersona(
        name: 'The Philosopher',
        description:
            'A wise and contemplative coach who helps you explore deeper meaning and purpose. Encourages reflection, self-discovery, and the examination of life\'s bigger questions to find authentic direction.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'contemplative',
        specialties: ['meaning-making', 'self-discovery', 'purpose-finding'],
        approach: 'reflective',
        videoUrl: 'assets/persona-videos/PhilosopherVideo.mp4',
      ),

      SystemPersona(
        name: 'The Pragmatist',
        description:
            'A practical and results-oriented coach focused on actionable solutions and real-world implementation. Cuts through theory to deliver concrete steps and measurable outcomes that work in everyday life.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'practical',
        specialties: [
          'implementation',
          'results-focus',
          'actionable-solutions',
        ],
        approach: 'no-nonsense',
        videoUrl: 'assets/persona-videos/PragmatistVideo.mp4',
      ),

      SystemPersona(
        name: 'The Catalyst',
        description:
            'An energetic and transformative coach who accelerates change and breakthrough moments. Specializes in creating momentum, facilitating rapid transformation, and helping you make quantum leaps in personal growth.',
        avatarUrl: 'assets/persona-profile-icon.jpg',
        isActive: true,
        coachingStyle: 'transformative',
        specialties: [
          'change-acceleration',
          'breakthrough-facilitation',
          'momentum-building',
        ],
        approach: 'high-energy',
        videoUrl: 'assets/persona-videos/CatalystVideo.mp4',
      ),
    ];
  }

  /// Seeds the default SystemPersona entities into Firestore
  /// Returns the list of created document IDs
  static Future<List<String>> seedDefaultPersonas() async {
    final personas = getDefaultPersonas();
    return await FirestoreService.createSystemPersonas(personas);
  }

  /// Checks if SystemPersona entities already exist in Firestore
  /// Returns true if personas exist, false if the collection is empty
  static Future<bool> personasExist() async {
    final count = await FirestoreService.getSystemPersonaCount();
    return count > 0;
  }

  /// Seeds personas only if they don't already exist
  /// Returns the list of created document IDs, or empty list if personas already exist
  static Future<List<String>> seedIfEmpty() async {
    if (await personasExist()) {
      return [];
    }
    return await seedDefaultPersonas();
  }
}

import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('PathStepProgress JSON Serialization', () {
    test('should handle null DateTime fields correctly', () {
      // Create a PathStepProgress with null DateTime fields
      final pathStepProgress = PathStepProgress(
        stepId: 'step_1',
        chatId: null,
        status: 'not_started',
        startedDate: null,
        completedDate: null,
      );

      // Convert to JSON
      final json = pathStepProgress.toJson();

      // Verify JSON structure
      expect(json['stepId'], equals('step_1'));
      expect(json['chatId'], isNull);
      expect(json['status'], equals('not_started'));
      expect(json['startedDate'], isNull);
      expect(json['completedDate'], isNull);

      // Convert back from JSON
      final fromJson = PathStepProgress.fromJson(json);

      // Verify all fields are correctly deserialized
      expect(fromJson.stepId, equals('step_1'));
      expect(fromJson.chatId, isNull);
      expect(fromJson.status, equals('not_started'));
      expect(fromJson.startedDate, isNull);
      expect(fromJson.completedDate, isNull);
    });

    test('should handle non-null DateTime fields correctly', () {
      final now = DateTime.now();
      
      // Create a PathStepProgress with non-null DateTime fields
      final pathStepProgress = PathStepProgress(
        stepId: 'step_2',
        chatId: 'chat_123',
        status: 'completed',
        startedDate: now,
        completedDate: now,
      );

      // Convert to JSON
      final json = pathStepProgress.toJson();

      // Convert back from JSON
      final fromJson = PathStepProgress.fromJson(json);

      // Verify all fields are correctly deserialized
      expect(fromJson.stepId, equals('step_2'));
      expect(fromJson.chatId, equals('chat_123'));
      expect(fromJson.status, equals('completed'));
      expect(fromJson.startedDate, isNotNull);
      expect(fromJson.completedDate, isNotNull);
    });

    test('should handle mixed null and non-null DateTime fields', () {
      final now = DateTime.now();
      
      // Create a PathStepProgress with mixed DateTime fields
      final pathStepProgress = PathStepProgress(
        stepId: 'step_3',
        chatId: 'chat_456',
        status: 'in_progress',
        startedDate: now,
        completedDate: null, // Still in progress, so no completion date
      );

      // Convert to JSON
      final json = pathStepProgress.toJson();

      // Convert back from JSON
      final fromJson = PathStepProgress.fromJson(json);

      // Verify all fields are correctly deserialized
      expect(fromJson.stepId, equals('step_3'));
      expect(fromJson.chatId, equals('chat_456'));
      expect(fromJson.status, equals('in_progress'));
      expect(fromJson.startedDate, isNotNull);
      expect(fromJson.completedDate, isNull);
    });
  });
}

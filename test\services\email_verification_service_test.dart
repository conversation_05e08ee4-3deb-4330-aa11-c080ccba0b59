import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:upshift/services/email_verification_service.dart';
import 'package:upshift/services/email_verification_interface.dart';

// Generate mocks for the interface
@GenerateMocks([EmailVerificationInterface])
import 'email_verification_service_test.mocks.dart';

void main() {
  group('EmailVerificationService', () {
    late MockEmailVerificationInterface mockInterface;

    setUp(() {
      mockInterface = MockEmailVerificationInterface();
      EmailVerificationService.setInstance(mockInterface);
    });

    group('isEmailVerified', () {
      test('returns true when user email is verified', () {
        // Arrange
        when(mockInterface.isEmailVerified()).thenReturn(true);

        // Act
        final result = EmailVerificationService.isEmailVerified();

        // Assert
        expect(result, true);
        verify(mockInterface.isEmailVerified()).called(1);
      });

      test('returns false when user email is not verified', () {
        // Arrange
        when(mockInterface.isEmailVerified()).thenReturn(false);

        // Act
        final result = EmailVerificationService.isEmailVerified();

        // Assert
        expect(result, false);
        verify(mockInterface.isEmailVerified()).called(1);
      });
    });

    group('getCurrentUserEmail', () {
      test('returns user email when user is signed in', () {
        // Arrange
        const testEmail = '<EMAIL>';
        when(mockInterface.getCurrentUserEmail()).thenReturn(testEmail);

        // Act
        final result = EmailVerificationService.getCurrentUserEmail();

        // Assert
        expect(result, testEmail);
        verify(mockInterface.getCurrentUserEmail()).called(1);
      });

      test('returns null when no user is signed in', () {
        // Arrange
        when(mockInterface.getCurrentUserEmail()).thenReturn(null);

        // Act
        final result = EmailVerificationService.getCurrentUserEmail();

        // Assert
        expect(result, null);
        verify(mockInterface.getCurrentUserEmail()).called(1);
      });
    });

    group('needsEmailVerification', () {
      test('returns true for unverified email/password user', () {
        // Arrange
        when(mockInterface.needsEmailVerification()).thenReturn(true);

        // Act
        final result = EmailVerificationService.needsEmailVerification();

        // Assert
        expect(result, true);
        verify(mockInterface.needsEmailVerification()).called(1);
      });

      test('returns false for verified user', () {
        // Arrange
        when(mockInterface.needsEmailVerification()).thenReturn(false);

        // Act
        final result = EmailVerificationService.needsEmailVerification();

        // Assert
        expect(result, false);
        verify(mockInterface.needsEmailVerification()).called(1);
      });
    });

    group('getUserCreationTime', () {
      test('returns creation time when user is signed in', () {
        // Arrange
        final testTime = DateTime.now();
        when(mockInterface.getUserCreationTime()).thenReturn(testTime);

        // Act
        final result = EmailVerificationService.getUserCreationTime();

        // Assert
        expect(result, testTime);
        verify(mockInterface.getUserCreationTime()).called(1);
      });

      test('returns null when no user is signed in', () {
        // Arrange
        when(mockInterface.getUserCreationTime()).thenReturn(null);

        // Act
        final result = EmailVerificationService.getUserCreationTime();

        // Assert
        expect(result, null);
        verify(mockInterface.getUserCreationTime()).called(1);
      });
    });

    group('isRecentRegistration', () {
      test('returns true for user created within 5 minutes', () {
        // Arrange
        when(mockInterface.isRecentRegistration()).thenReturn(true);

        // Act
        final result = EmailVerificationService.isRecentRegistration();

        // Assert
        expect(result, true);
        verify(mockInterface.isRecentRegistration()).called(1);
      });

      test('returns false for user created more than 5 minutes ago', () {
        // Arrange
        when(mockInterface.isRecentRegistration()).thenReturn(false);

        // Act
        final result = EmailVerificationService.isRecentRegistration();

        // Assert
        expect(result, false);
        verify(mockInterface.isRecentRegistration()).called(1);
      });
    });

    group('sendEmailVerification', () {
      test('delegates to interface implementation', () async {
        // Arrange
        when(
          mockInterface.sendEmailVerification(),
        ).thenAnswer((_) async => true);

        // Act
        final result = await EmailVerificationService.sendEmailVerification();

        // Assert
        expect(result, true);
        verify(mockInterface.sendEmailVerification()).called(1);
      });
    });

    group('reloadUserAndCheckVerification', () {
      test('delegates to interface implementation', () async {
        // Arrange
        when(
          mockInterface.reloadUserAndCheckVerification(),
        ).thenAnswer((_) async => true);

        // Act
        final result =
            await EmailVerificationService.reloadUserAndCheckVerification();

        // Assert
        expect(result, true);
        verify(mockInterface.reloadUserAndCheckVerification()).called(1);
      });
    });

    group('resendVerificationEmail', () {
      test('delegates to interface implementation', () async {
        // Arrange
        when(
          mockInterface.resendVerificationEmail(),
        ).thenAnswer((_) async => true);

        // Act
        final result = await EmailVerificationService.resendVerificationEmail();

        // Assert
        expect(result, true);
        verify(mockInterface.resendVerificationEmail()).called(1);
      });
    });

    group('signOut', () {
      test('delegates to interface implementation', () async {
        // Arrange
        when(mockInterface.signOut()).thenAnswer((_) async {});

        // Act
        await EmailVerificationService.signOut();

        // Assert
        verify(mockInterface.signOut()).called(1);
      });
    });
  });
}

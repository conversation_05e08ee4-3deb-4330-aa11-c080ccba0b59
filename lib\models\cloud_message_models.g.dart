// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cloud_message_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EngagementData _$EngagementDataFromJson(Map<String, dynamic> json) =>
    EngagementData(
      title: json['title'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$EngagementDataToJson(EngagementData instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
    };

GoalsAndProgressData _$GoalsAndProgressDataFromJson(
  Map<String, dynamic> json,
) => GoalsAndProgressData(
  goalName: json['goalName'] as String,
  goalId: json['goalId'] as String,
  description: json['description'] as String,
  progressPercentage: (json['progressPercentage'] as num).toInt(),
  currentStep: json['currentStep'] as String,
);

Map<String, dynamic> _$GoalsAndProgressDataToJson(
  GoalsAndProgressData instance,
) => <String, dynamic>{
  'goalName': instance.goalName,
  'goalId': instance.goalId,
  'description': instance.description,
  'progressPercentage': instance.progressPercentage,
  'currentStep': instance.currentStep,
};

GuidedPathUpdatesData _$GuidedPathUpdatesDataFromJson(
  Map<String, dynamic> json,
) => GuidedPathUpdatesData(
  pathId: json['pathId'] as String,
  pathName: json['pathName'] as String,
  stepId: json['stepId'] as String,
  stepName: json['stepName'] as String,
  imageUrl: json['imageUrl'] as String?,
);

Map<String, dynamic> _$GuidedPathUpdatesDataToJson(
  GuidedPathUpdatesData instance,
) => <String, dynamic>{
  'pathId': instance.pathId,
  'pathName': instance.pathName,
  'stepId': instance.stepId,
  'stepName': instance.stepName,
  'imageUrl': instance.imageUrl,
};

SystemAnnouncementsData _$SystemAnnouncementsDataFromJson(
  Map<String, dynamic> json,
) => SystemAnnouncementsData(
  announcementId: json['announcementId'] as String,
  announcementTitle: json['announcementTitle'] as String,
  announcementBody: json['announcementBody'] as String,
  imageUrl: json['imageUrl'] as String?,
);

Map<String, dynamic> _$SystemAnnouncementsDataToJson(
  SystemAnnouncementsData instance,
) => <String, dynamic>{
  'announcementId': instance.announcementId,
  'announcementTitle': instance.announcementTitle,
  'announcementBody': instance.announcementBody,
  'imageUrl': instance.imageUrl,
};

WeeklyInsightsData _$WeeklyInsightsDataFromJson(Map<String, dynamic> json) =>
    WeeklyInsightsData(
      insightId: json['insightId'] as String,
      insightTitle: json['insightTitle'] as String,
      insightBody: json['insightBody'] as String,
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$WeeklyInsightsDataToJson(WeeklyInsightsData instance) =>
    <String, dynamic>{
      'insightId': instance.insightId,
      'insightTitle': instance.insightTitle,
      'insightBody': instance.insightBody,
      'imageUrl': instance.imageUrl,
    };

# Upshift Theme System

This comprehensive theme system embodies Upshift's brand identity as "Your Personal AI Coach for Meaningful Growth." The design philosophy "Growth Through Clarity" is reflected throughout the visual design, color choices, typography, and component styling.

## Design Philosophy: "Growth Through Clarity"

The theme creates a sense of forward momentum, personal empowerment, and thoughtful progression through:

- **Inspiring Progress**: Upward gradients, subtle animations, and progressive visual elements
- **Fostering Trust**: Clean, consistent layouts with generous whitespace and clear hierarchy
- **Encouraging Reflection**: Calm, balanced compositions that don't overwhelm
- **Celebrating Achievement**: Meaningful visual feedback for progress and milestones
- **Maintaining Accessibility**: High contrast ratios and inclusive design principles

## Theme Components

### 1. Colors (`AppColors`)

#### Primary Palette
- **Primary**: `#2E7D32` (Forest Green) - Growth, stability, progress
- **Secondary**: `#5E35B1` (Deep Purple) - Transformation, insight

#### Category Colors
- **Focus & Productivity**: `#1976D2` (Professional Blue)
- **Mindset & Resilience**: `#388E3C` (Growth Green)
- **Habit Formation**: `#F57C00` (Energy Orange)
- **Life Design**: `#7B1FA2` (Vision Purple)

#### Usage Examples
```dart
// Direct color access
Container(color: AppColors.primary)

// Category colors
Container(color: AppColors.getCategoryColor('Focus & Productivity'))

// Theme-aware colors
Container(color: context.getCategoryColor('Mindset & Resilience'))
```

### 2. Typography (`AppTypography`)

Uses **Inter** font family for modern, highly readable design with comprehensive text styles following Material Design 3 guidelines.

#### Usage Examples
```dart
// Standard text styles
Text('Title', style: AppTypography.textTheme.headlineLarge)

// Custom text styles
Text('Chat message', style: AppTypography.chatMessage)

// Responsive text
Text('Responsive', style: AppTypography.textTheme.bodyLarge.responsive(context))
```

### 3. Icons (`AppIcons`)

Mixed iconography style: outlined for navigation/actions, filled for status/achievements.

#### Usage Examples
```dart
// Navigation icons
Icon(AppIcons.getNavigationIcon('home', selected: true))

// Category icons
Icon(AppIcons.getCategoryIcon('Focus & Productivity'))

// Status icons
Icon(AppIcons.getStatusIcon('completed'))
```

### 4. Dimensions (`AppDimensions`)

Consistent spacing, sizing, and layout measurements based on 8dp grid system.

#### Usage Examples
```dart
// Standard spacing
Padding(padding: AppDimensions.paddingM)

// Responsive spacing
Padding(padding: context.responsivePadding)

// Border radius
Container(decoration: BoxDecoration(borderRadius: AppDimensions.borderRadiusM))
```

## Theme Extensions

### Context Extensions
```dart
// Easy access to theme properties
context.colorScheme
context.textTheme
context.isDarkMode
context.getCategoryColor('Life Design')
context.getStatusColor('completed')
```

### TextStyle Extensions
```dart
// Modify text styles fluently
AppTypography.textTheme.bodyLarge
  .withColor(AppColors.primary)
  .withWeight(FontWeight.bold)
  .responsive(context)
```

### Helper Widgets

#### ThemeAwareWidget
```dart
ThemeAwareWidget(
  builder: (context, colorScheme, textTheme) {
    return Container(
      color: colorScheme.surface,
      child: Text('Hello', style: textTheme.bodyLarge),
    );
  },
)
```

#### CategoryThemedWidget
```dart
CategoryThemedWidget(
  category: 'Focus & Productivity',
  builder: (context, categoryColor, categoryIcon) {
    return Container(
      color: categoryColor.withOpacity(0.1),
      child: Icon(categoryIcon, color: categoryColor),
    );
  },
)
```

#### ResponsiveWidget
```dart
ResponsiveWidget(
  builder: (context, isMobile, isTablet, isDesktop) {
    return Container(
      width: isMobile ? 200 : 400,
      child: Text('Responsive content'),
    );
  },
)
```

## Usage Guidelines

### 1. Importing the Theme
```dart
import 'package:upshift/theme/theme.dart';
```

### 2. Setting Up in MaterialApp
```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
  // ...
)
```

### 3. Using in Widgets
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: AppDimensions.paddingM,
      decoration: BoxDecoration(
        color: context.colorScheme.surface,
        borderRadius: AppDimensions.borderRadiusM,
      ),
      child: Text(
        'Hello World',
        style: context.textTheme.headlineMedium?.withColor(
          context.getCategoryColor('Mindset & Resilience'),
        ),
      ),
    );
  }
}
```

### 4. Creating Category-Themed Components
```dart
Widget buildCategoryCard(String category) {
  return CategoryThemedWidget(
    category: category,
    builder: (context, categoryColor, categoryIcon) {
      return Card(
        child: Container(
          decoration: ThemeDecorations.category(context, category),
          child: Column(
            children: [
              Icon(categoryIcon, color: categoryColor),
              Text(
                category,
                style: ThemeTextStyles.categoryTag(context, category),
              ),
            ],
          ),
        ),
      );
    },
  );
}
```

## Cross-Platform Considerations

### iOS Specific
- Respects safe areas and notch
- Uses iOS-style navigation patterns
- Slightly larger line heights for readability
- iOS-style spring animations

### Android Specific
- Supports Material You dynamic theming (Android 12+)
- Uses Android-style navigation patterns
- Material ripple effects for interactions
- Adaptive status bar styling

### Accessibility
- WCAG 2.1 AA compliance
- Minimum 44px touch targets
- 4.5:1 contrast ratio for normal text
- 3:1 contrast ratio for large text
- Respects reduced motion preferences

## Migration Guide

To migrate existing components to use the new theme system:

1. **Import the theme**: Add `import 'package:upshift/theme/theme.dart';`

2. **Replace hardcoded colors**: 
   ```dart
   // Before
   color: Colors.green
   
   // After
   color: AppColors.primary
   // or
   color: context.colorScheme.primary
   ```

3. **Use category colors**:
   ```dart
   // Before
   Color getCategoryColor(String category) {
     switch (category) {
       case 'Focus & Productivity': return Colors.blue;
       // ...
     }
   }
   
   // After
   Color getCategoryColor(String category) {
     return context.getCategoryColor(category);
   }
   ```

4. **Apply consistent spacing**:
   ```dart
   // Before
   padding: EdgeInsets.all(16)
   
   // After
   padding: AppDimensions.paddingM
   ```

5. **Use theme-aware text styles**:
   ```dart
   // Before
   style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)
   
   // After
   style: context.textTheme.headlineMedium
   ```

This theme system provides a solid foundation for consistent, accessible, and beautiful UI across the entire Upshift application while embodying the brand's core values of growth, clarity, and meaningful progress.

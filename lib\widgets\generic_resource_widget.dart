import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/models.dart' as models;

/// A generic widget for displaying non-video external resources
/// such as articles, podcasts, tools, books, courses, websites, etc.
class GenericResourceWidget extends StatelessWidget {
  final models.ExternalResource resource;

  const GenericResourceWidget({super.key, required this.resource});

  /// Launch the resource URL in external application
  Future<void> _launchUrl(BuildContext context, String url) async {
    try {
      final uri = Uri.parse(url);

      // Try launching with external application mode first
      bool launched = false;
      try {
        launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
      } catch (e) {
        // If external application mode fails, try platform default
        try {
          launched = await launchUrl(uri, mode: LaunchMode.platformDefault);
        } catch (e2) {
          // If platform default fails, try in-app web view as last resort
          launched = await launchUrl(uri, mode: LaunchMode.inAppWebView);
        }
      }

      if (!launched && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Could not launch $url')));
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error launching URL: $e')));
      }
    }
  }

  /// Get icon for external resource type
  IconData _getResourceTypeIcon(models.ExternalResourceType type) {
    switch (type) {
      case models.ExternalResourceType.article:
        return Icons.article;
      case models.ExternalResourceType.video:
        return Icons.play_circle_outline;
      case models.ExternalResourceType.podcast:
        return Icons.podcasts;
      case models.ExternalResourceType.tool:
        return Icons.build;
      case models.ExternalResourceType.book:
        return Icons.menu_book;
      case models.ExternalResourceType.course:
        return Icons.school;
      case models.ExternalResourceType.website:
        return Icons.language;
      case models.ExternalResourceType.other:
        return Icons.link;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _launchUrl(context, resource.link),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getResourceTypeIcon(resource.type),
                    size: 20,
                    color: colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      resource.title,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  // Resource type badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      resource.type.displayName,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: colorScheme.onPrimaryContainer,
                        fontSize: 10,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(Icons.open_in_new, size: 16, color: colorScheme.primary),
                ],
              ),
              if (resource.description != null) ...[
                const SizedBox(height: 8),
                Text(
                  resource.description!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
              if (resource.source != null ||
                  resource.durationMinutes != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (resource.source != null) ...[
                      Icon(
                        Icons.source,
                        size: 12,
                        color: colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        resource.source!,
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.5),
                          fontSize: 10,
                        ),
                      ),
                    ],
                    if (resource.source != null &&
                        resource.durationMinutes != null) ...[
                      const SizedBox(width: 12),
                      Text(
                        '•',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.5),
                          fontSize: 10,
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                    if (resource.durationMinutes != null) ...[
                      Icon(
                        Icons.schedule,
                        size: 12,
                        color: colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${resource.durationMinutes} min',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.5),
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

{"$id": "https://example.com/schemas/guided_paths.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "GuidedPaths", "description": "Schema for Upshift guided paths and their steps", "type": "array", "items": {"type": "object", "properties": {"path": {"$ref": "#/definitions/GuidedPath"}, "steps": {"type": "array", "items": {"$ref": "#/definitions/PathStep"}, "minItems": 1}}, "required": ["path", "steps"], "additionalProperties": false}, "definitions": {"GuidedPath": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the guided path (optional for seeding)"}, "name": {"type": "string", "minLength": 1, "description": "Name of the guided path"}, "category": {"type": "string", "enum": ["Focus & Productivity", "Mindset & Resilience", "Habit Formation", "Life Design", "Foundations of Self-Awareness", "Mindset & Emotional Mastery"], "description": "Category of the guided path"}, "description": {"type": "string", "minLength": 1, "description": "Detailed description of the path"}, "stepCount": {"type": "integer", "minimum": 1, "description": "Number of steps in the path"}, "targetUserTier": {"type": "string", "enum": ["free", "paid"], "description": "Target user tier for access control"}, "imageUrl": {"type": "string", "description": "URL or path to the path's cover image"}, "estimatedCompletionTimeMinutes": {"type": "integer", "minimum": 1, "description": "Estimated time to complete the entire path in minutes"}, "difficultyLevel": {"type": "string", "enum": ["beginner", "intermediate", "advanced"], "description": "Difficulty level of the path"}, "prerequisites": {"type": "array", "items": {"type": "string"}, "description": "List of prerequisites for this path"}, "isActive": {"type": "boolean", "description": "Whether the path is active and available"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp in ISO 8601 format"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp in ISO 8601 format"}, "metadata": {"type": "object", "properties": {"featured": {"type": "boolean", "description": "Whether this path is featured"}, "category_order": {"type": "integer", "minimum": 1, "description": "Order within the category"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Tags associated with the path"}}, "additionalProperties": true, "description": "Additional metadata for the path"}}, "required": ["name", "category", "description", "stepCount", "targetUserTier", "isActive", "createdAt"], "additionalProperties": false}, "PathStep": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the step (optional for seeding)"}, "pathId": {"type": "string", "description": "Reference to the parent guided path (set during seeding)"}, "stepNumber": {"type": "integer", "minimum": 1, "description": "Sequential step number within the path"}, "title": {"type": "string", "minLength": 1, "description": "Title of the step"}, "description": {"type": "string", "minLength": 1, "description": "Detailed description of the step"}, "completionCriteria": {"type": "string", "minLength": 1, "description": "Criteria for completing this step"}, "estimatedDurationMinutes": {"type": "integer", "minimum": 1, "description": "Estimated time to complete this step in minutes"}, "resources": {"type": "array", "items": {"type": "string"}, "description": "List of resources (URLs, documents, etc.)"}, "reflectionPrompts": {"type": "array", "items": {"type": "string"}, "description": "List of reflection questions for the user"}, "isActive": {"type": "boolean", "description": "Whether the step is active and available"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp in ISO 8601 format"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp in ISO 8601 format"}, "metadata": {"type": "object", "properties": {"step_type": {"type": "string", "enum": ["foundation", "skill-building", "assessment", "technique", "reflection", "sustainability", "discovery", "practice", "toolkit", "integration", "design", "measurement", "support", "creation", "strategy", "planning", "implementation"], "description": "Type of step for categorization"}, "difficulty": {"type": "string", "enum": ["easy", "medium", "hard"], "description": "Difficulty level of this specific step"}}, "additionalProperties": true, "description": "Additional metadata for the step"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "title", "description", "completionCriteria", "isActive", "createdAt"], "additionalProperties": false}}}
import 'package:flutter/material.dart';
import '../services/email_verification_service.dart';
import '../theme/theme.dart';

/// Email verification page that guides users through the email verification process
///
/// This page is shown when a user needs to verify their email address before
/// accessing the main application. It provides options to:
/// - Check verification status
/// - Resend verification email
/// - Sign out and use a different account
class EmailVerificationPage extends StatefulWidget {
  const EmailVerificationPage({super.key});

  @override
  State<EmailVerificationPage> createState() => _EmailVerificationPageState();
}

class _EmailVerificationPageState extends State<EmailVerificationPage> {
  bool _isLoading = false;
  bool _isCheckingVerification = false;
  String? _errorMessage;
  String? _successMessage;
  DateTime? _lastEmailSent;

  @override
  void initState() {
    super.initState();
    _checkIfAlreadyVerified();
  }

  /// Check if the user is already verified and navigate away if so
  Future<void> _checkIfAlreadyVerified() async {
    if (EmailVerificationService.isEmailVerified()) {
      // User is already verified, this page shouldn't be shown
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  /// Check verification status by reloading user data
  Future<void> _checkVerificationStatus() async {
    setState(() {
      _isCheckingVerification = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final isVerified =
          await EmailVerificationService.reloadUserAndCheckVerification();

      if (isVerified) {
        setState(() {
          _successMessage = 'Email verified successfully!';
        });

        // Wait a moment to show success message, then navigate
        await Future.delayed(const Duration(seconds: 1));
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        setState(() {
          _errorMessage =
              'Email not yet verified. Please check your inbox and click the verification link.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to check verification status: $e';
      });
    } finally {
      setState(() {
        _isCheckingVerification = false;
      });
    }
  }

  /// Resend verification email
  Future<void> _resendVerificationEmail() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final success = await EmailVerificationService.resendVerificationEmail();

      if (success) {
        setState(() {
          _successMessage = 'Verification email sent! Please check your inbox.';
          _lastEmailSent = DateTime.now();
        });
      } else {
        setState(() {
          _errorMessage =
              'Too many verification emails sent. Please wait before requesting another.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceFirst('Exception: ', '');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Sign out the current user
  Future<void> _signOut() async {
    try {
      await EmailVerificationService.signOut();
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to sign out: $e';
      });
    }
  }

  /// Check if resend button should be disabled (rate limiting)
  bool _canResendEmail() {
    if (_lastEmailSent == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastEmailSent!);
    return difference.inMinutes >= 1; // Allow resend after 1 minute
  }

  @override
  Widget build(BuildContext context) {
    final userEmail = EmailVerificationService.getCurrentUserEmail();

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: AppDimensions.paddingL,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Email verification icon
              Center(
                child: Icon(
                  AppIcons.emailUnverified,
                  size: AppDimensions.iconXxl * 1.5,
                  color: AppColors.warning,
                ),
              ),
              SizedBox(height: AppDimensions.spacingL),

              // Title
              Text(
                'Verify Your Email',
                style: context.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingM),

              // Description
              Text(
                'We\'ve sent a verification link to:',
                style: context.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingS),

              // Email address
              if (userEmail != null)
                Container(
                  padding: AppDimensions.paddingM,
                  decoration: BoxDecoration(
                    color: context.colorScheme.surfaceContainerHighest,
                    borderRadius: AppDimensions.borderRadiusM,
                  ),
                  child: Text(
                    userEmail,
                    style: context.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              SizedBox(height: AppDimensions.spacingL),

              // Instructions
              Text(
                'Please check your email and click the verification link to continue. You may need to check your spam folder.',
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingXl),

              // Error message
              if (_errorMessage != null)
                Container(
                  padding: AppDimensions.paddingM,
                  margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    borderRadius: AppDimensions.borderRadiusM,
                    border: Border.all(
                      color: AppColors.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(AppIcons.error, color: AppColors.error, size: 20),
                      SizedBox(width: AppDimensions.spacingS),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: context.textTheme.bodySmall?.copyWith(
                            color: AppColors.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Success message
              if (_successMessage != null)
                Container(
                  padding: AppDimensions.paddingM,
                  margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.1),
                    borderRadius: AppDimensions.borderRadiusM,
                    border: Border.all(
                      color: AppColors.success.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        AppIcons.success,
                        color: AppColors.success,
                        size: 20,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Expanded(
                        child: Text(
                          _successMessage!,
                          style: context.textTheme.bodySmall?.copyWith(
                            color: AppColors.success,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Check verification button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isCheckingVerification
                      ? null
                      : _checkVerificationStatus,
                  icon: _isCheckingVerification
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              context.colorScheme.onPrimary,
                            ),
                          ),
                        )
                      : Icon(AppIcons.emailCheck),
                  label: Text(
                    _isCheckingVerification
                        ? 'Checking...'
                        : 'I\'ve Verified My Email',
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.spacingM,
                    ),
                  ),
                ),
              ),
              SizedBox(height: AppDimensions.spacingM),

              // Resend email button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: (_isLoading || !_canResendEmail())
                      ? null
                      : _resendVerificationEmail,
                  icon: _isLoading
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              context.colorScheme.primary,
                            ),
                          ),
                        )
                      : Icon(AppIcons.emailResend),
                  label: Text(
                    _isLoading ? 'Sending...' : 'Resend Verification Email',
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.spacingM,
                    ),
                  ),
                ),
              ),
              SizedBox(height: AppDimensions.spacingXl),

              // Sign out button
              TextButton(
                onPressed: _signOut,
                child: Text(
                  'Sign in with a different account',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

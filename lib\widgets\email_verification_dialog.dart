import 'package:flutter/material.dart';
import '../services/email_verification_service.dart';
import '../theme/theme.dart';

/// Dialog for email verification actions
///
/// This dialog can be used to prompt users for email verification
/// without navigating to a full page. Useful for inline verification prompts.
class EmailVerificationDialog extends StatefulWidget {
  /// The user's email address to display
  final String? userEmail;

  /// Callback when verification is completed successfully
  final VoidCallback? onVerificationComplete;

  /// Whether to show the resend option
  final bool showResendOption;

  const EmailVerificationDialog({
    super.key,
    this.userEmail,
    this.onVerificationComplete,
    this.showResendOption = true,
  });

  @override
  State<EmailVerificationDialog> createState() =>
      _EmailVerificationDialogState();
}

class _EmailVerificationDialogState extends State<EmailVerificationDialog> {
  bool _isLoading = false;
  bool _isCheckingVerification = false;
  String? _errorMessage;
  String? _successMessage;

  /// Check verification status
  Future<void> _checkVerificationStatus() async {
    setState(() {
      _isCheckingVerification = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final isVerified =
          await EmailVerificationService.reloadUserAndCheckVerification();

      if (isVerified) {
        setState(() {
          _successMessage = 'Email verified successfully!';
        });

        // Wait a moment to show success message
        await Future.delayed(const Duration(seconds: 1));

        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate success
          widget.onVerificationComplete?.call();
        }
      } else {
        setState(() {
          _errorMessage = 'Email not yet verified. Please check your inbox.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to check verification status: $e';
      });
    } finally {
      setState(() {
        _isCheckingVerification = false;
      });
    }
  }

  /// Resend verification email
  Future<void> _resendVerificationEmail() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final success = await EmailVerificationService.resendVerificationEmail();

      if (success) {
        setState(() {
          _successMessage = 'Verification email sent! Please check your inbox.';
        });
      } else {
        setState(() {
          _errorMessage =
              'Too many verification emails sent. Please wait before requesting another.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceFirst('Exception: ', '');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      icon: Icon(
        AppIcons.emailUnverified,
        color: AppColors.warning,
        size: AppDimensions.iconXl,
      ),
      title: Text(
        'Email Verification Required',
        style: context.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Description
          Text(
            'Please verify your email address to continue using Upshift.',
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),

          if (widget.userEmail != null) ...[
            SizedBox(height: AppDimensions.spacingM),
            Container(
              padding: AppDimensions.paddingS,
              decoration: BoxDecoration(
                color: context.colorScheme.surfaceContainerHighest,
                borderRadius: AppDimensions.borderRadiusS,
              ),
              child: Text(
                widget.userEmail!,
                style: context.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],

          // Error message
          if (_errorMessage != null) ...[
            SizedBox(height: AppDimensions.spacingM),
            Container(
              padding: AppDimensions.paddingS,
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: AppDimensions.borderRadiusS,
                border: Border.all(
                  color: AppColors.error.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(AppIcons.error, color: AppColors.error, size: 16),
                  SizedBox(width: AppDimensions.spacingXs),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Success message
          if (_successMessage != null) ...[
            SizedBox(height: AppDimensions.spacingM),
            Container(
              padding: AppDimensions.paddingS,
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: AppDimensions.borderRadiusS,
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(AppIcons.success, color: AppColors.success, size: 16),
                  SizedBox(width: AppDimensions.spacingXs),
                  Expanded(
                    child: Text(
                      _successMessage!,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: AppColors.success,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Later'),
        ),

        // Resend button (if enabled)
        if (widget.showResendOption)
          TextButton(
            onPressed: _isLoading ? null : _resendVerificationEmail,
            child: _isLoading
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        context.colorScheme.primary,
                      ),
                    ),
                  )
                : const Text('Resend'),
          ),

        // Check verification button
        ElevatedButton(
          onPressed: _isCheckingVerification ? null : _checkVerificationStatus,
          child: _isCheckingVerification
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      context.colorScheme.onPrimary,
                    ),
                  ),
                )
              : const Text('Check'),
        ),
      ],
    );
  }
}

/// Utility function to show the email verification dialog
///
/// Returns true if verification was completed, false if dismissed
Future<bool?> showEmailVerificationDialog(
  BuildContext context, {
  String? userEmail,
  VoidCallback? onVerificationComplete,
  bool showResendOption = true,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => EmailVerificationDialog(
      userEmail: userEmail,
      onVerificationComplete: onVerificationComplete,
      showResendOption: showResendOption,
    ),
  );
}

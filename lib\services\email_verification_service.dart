import 'email_verification_interface.dart';

/// Service class for handling email verification operations
///
/// This service provides static methods that delegate to an underlying
/// EmailVerificationInterface implementation for easier testing and dependency injection.
class EmailVerificationService {
  static EmailVerificationInterface _instance =
      FirebaseEmailVerificationService();

  /// Set a custom implementation for testing purposes
  static void setInstance(EmailVerificationInterface instance) {
    _instance = instance;
  }

  /// Reset to the default Firebase implementation
  static void resetToDefault() {
    _instance = FirebaseEmailVerificationService();
  }

  /// Send email verification to the current user
  ///
  /// Returns true if the email was sent successfully, false otherwise
  /// Throws an exception if no user is signed in or if sending fails
  static Future<bool> sendEmailVerification() async {
    return _instance.sendEmailVerification();
  }

  /// Check if the current user's email is verified
  ///
  /// Returns true if verified, false if not verified or no user signed in
  static bool isEmailVerified() {
    return _instance.isEmailVerified();
  }

  /// Reload the current user to get the latest verification status
  ///
  /// This is useful after the user has clicked the verification link
  /// Returns the updated verification status
  static Future<bool> reloadUserAndCheckVerification() async {
    return _instance.reloadUserAndCheckVerification();
  }

  /// Get the current user's email address
  ///
  /// Returns null if no user is signed in
  static String? getCurrentUserEmail() {
    return _instance.getCurrentUserEmail();
  }

  /// Check if the current user needs email verification
  ///
  /// Returns true if:
  /// - User is signed in
  /// - User has an email address
  /// - Email is not verified
  /// - User signed up with email/password (not OAuth)
  static bool needsEmailVerification() {
    return _instance.needsEmailVerification();
  }

  /// Resend verification email with rate limiting check
  ///
  /// Returns true if email was sent, false if rate limited
  /// Throws exception on other errors
  static Future<bool> resendVerificationEmail() async {
    return _instance.resendVerificationEmail();
  }

  /// Listen to auth state changes for verification status
  ///
  /// Returns a stream that emits true when email becomes verified
  static Stream<bool> get verificationStatusStream {
    return _instance.verificationStatusStream;
  }

  /// Sign out the current user
  ///
  /// Useful when user wants to sign in with a different account
  static Future<void> signOut() async {
    return _instance.signOut();
  }

  /// Get user creation time to determine if this is a new registration
  ///
  /// Returns null if no user is signed in
  static DateTime? getUserCreationTime() {
    return _instance.getUserCreationTime();
  }

  /// Check if this is a recent registration (within last 5 minutes)
  ///
  /// Useful for determining if we should automatically send verification
  static bool isRecentRegistration() {
    return _instance.isRecentRegistration();
  }
}
